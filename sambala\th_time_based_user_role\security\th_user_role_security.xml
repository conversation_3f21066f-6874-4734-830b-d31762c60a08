<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Role Manager Group -->
        <record id="group_th_role_manager" model="res.groups">
            <field name="name">Qu<PERSON>n lý Role theo thời gian</field>
            <field name="category_id" ref="base.module_category_administration"/>
            <field name="comment">Người dùng có thể quản lý role theo thời gian</field>
        </record>

        <!-- Role User Group -->
        <record id="group_th_role_user" model="res.groups">
            <field name="name">Ngư<PERSON>i dùng Role theo thời gian</field>
            <field name="category_id" ref="base.module_category_administration"/>
            <field name="comment">Người dùng có thể xem role theo thời gian của riêng họ</field>
        </record>

        <!-- Enhanced Access Rights -->
        <record id="access_th_user_role_manager" model="ir.model.access">
            <field name="name">th.user.role manager</field>
            <field name="model_id" ref="model_th_user_role"/>
            <field name="group_id" ref="group_th_role_manager"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <record id="access_th_user_role_line_manager" model="ir.model.access">
            <field name="name">th.user.role.line manager</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="group_id" ref="group_th_role_manager"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <record id="access_th_role_category_manager" model="ir.model.access">
            <field name="name">th.role.category manager</field>
            <field name="model_id" ref="model_th_role_category"/>
            <field name="group_id" ref="group_th_role_manager"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <record id="access_th_role_history_manager" model="ir.model.access">
            <field name="name">th.role.history manager</field>
            <field name="model_id" ref="model_th_role_history"/>
            <field name="group_id" ref="group_th_role_manager"/>
            <field name="perm_read">1</field>
            <field name="perm_write">1</field>
            <field name="perm_create">1</field>
            <field name="perm_unlink">1</field>
        </record>

        <!-- Record Rules -->
        <record id="rule_th_user_role_manager" model="ir.rule">
            <field name="name">Role Manager: full access</field>
            <field name="model_id" ref="model_th_user_role"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_th_role_manager'))]"/>
        </record>

        <record id="rule_th_user_role_line_manager" model="ir.rule">
            <field name="name">Role Line Manager: full access</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_th_role_manager'))]"/>
        </record>

        <record id="rule_th_user_role_line_user" model="ir.rule">
            <field name="name">Role Line User: own records only</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="domain_force">[('th_user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_th_role_user'))]"/>
        </record>

        <record id="rule_th_role_history_manager" model="ir.rule">
            <field name="name">Role History Manager: full access</field>
            <field name="model_id" ref="model_th_role_history"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_th_role_manager'))]"/>
        </record>

        <record id="rule_th_role_history_user" model="ir.rule">
            <field name="name">Role History User: own records only</field>
            <field name="model_id" ref="model_th_role_history"/>
            <field name="domain_force">[('th_user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_th_role_user'))]"/>
        </record>

    </data>
</odoo>
