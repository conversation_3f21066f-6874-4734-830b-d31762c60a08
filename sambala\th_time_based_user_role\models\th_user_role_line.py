# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class ThUserRoleLine(models.Model):
    _name = 'th.user.role.line'
    _description = 'Phân quyền Role người dùng theo thời gian'
    _order = 'th_date_from desc'
    _rec_name = 'display_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    th_role_id = fields.Many2one(
        'th.user.role',
        string='Role',
        required=True,
        tracking=True,
        help="Role cần gán"
    )

    th_user_id = fields.Many2one(
        'res.users',
        string='Người dùng',
        required=True,
        ondelete='cascade',
        tracking=True,
        help="User được gán role"
    )

    th_date_from = fields.Datetime(
        string='<PERSON><PERSON>y bắt đầu',
        required=True,
        default=fields.Datetime.now,
        tracking=True,
        help="Ngày và giờ role trở nên hoạt động"
    )

    th_date_to = fields.Datetime(
        string='Ng<PERSON>y kết thúc',
        tracking=True,
        help="Ngày và giờ role hết hạn (tùy chọn)"
    )

    th_is_enabled = fields.Boolean(
        string='Đang hoạt động',
        default=False,
        tracking=True,
        help="Role hiện tại có đang hoạt động cho user này không"
    )

    th_previous_is_enabled = fields.Boolean(
        string='Trạng thái trước đó',
        default=False,
        help="Trạng thái trước đó để theo dõi thay đổi"
    )

    th_applied_group_ids = fields.Many2many(
        'res.groups',
        'th_role_line_applied_group_rel',
        'line_id',
        'group_id',
        string='Nhóm đã áp dụng',
        help="Các group đã được áp dụng cho user với role này"
    )

    th_active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        tracking=True,
        help="Nếu bỏ chọn, phân quyền này sẽ bị bỏ qua"
    )

    th_notes = fields.Text(
        string='Ghi chú',
        help="Ghi chú bổ sung về phân quyền role này"
    )

    display_name = fields.Char(
        string='Tên hiển thị',
        compute='_compute_display_name',
        store=True
    )

    th_status = fields.Selection([
        ('pending', 'Chờ xử lý'),
        ('active', 'Hoạt động'),
        ('expired', 'Hết hạn'),
        ('disabled', 'Vô hiệu hóa')
    ], string='Trạng thái', compute='_compute_th_status', store=True)

    th_days_until_start = fields.Integer(
        string='Số ngày đến khi bắt đầu',
        compute='_compute_th_days_until_start',
        help="Số ngày cho đến khi role trở nên hoạt động"
    )

    th_days_until_end = fields.Integer(
        string='Số ngày đến khi kết thúc',
        compute='_compute_th_days_until_end',
        help="Số ngày cho đến khi role hết hạn"
    )

    @api.depends('th_role_id.name', 'th_user_id.name', 'th_date_from', 'th_date_to')
    def _compute_display_name(self):
        for line in self:
            role_name = line.th_role_id.name or ''
            user_name = line.th_user_id.name or ''
            date_from = line.th_date_from.strftime('%d/%m/%Y') if line.th_date_from else ''
            date_to = line.th_date_to.strftime('%d/%m/%Y') if line.th_date_to else 'Không có ngày kết thúc'
            line.display_name = f"{user_name} - {role_name} ({date_from} - {date_to})"

    @api.depends('th_is_enabled', 'th_active', 'th_date_from', 'th_date_to')
    def _compute_th_status(self):
        now = fields.Datetime.now()
        for line in self:
            if not line.th_active:
                line.th_status = 'disabled'
            elif not line.th_is_enabled and line.th_date_from > now:
                line.th_status = 'pending'
            elif line.th_is_enabled:
                line.th_status = 'active'
            elif line.th_date_to and line.th_date_to < now:
                line.th_status = 'expired'
            else:
                line.th_status = 'disabled'

    @api.depends('th_date_from')
    def _compute_th_days_until_start(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_from:
                delta = line.th_date_from - now
                line.th_days_until_start = delta.days
            else:
                line.th_days_until_start = 0

    @api.depends('th_date_to')
    def _compute_th_days_until_end(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_to:
                delta = line.th_date_to - now
                line.th_days_until_end = delta.days
            else:
                line.th_days_until_end = 999999  # No end date

    @api.constrains('th_date_from', 'th_date_to')
    def _check_dates(self):
        for line in self:
            if line.th_date_to and line.th_date_from >= line.th_date_to:
                raise ValidationError(_('Ngày kết thúc phải sau ngày bắt đầu'))

    @api.constrains('th_role_id', 'th_user_id', 'th_date_from', 'th_date_to')
    def _check_overlapping_assignments(self):
        for line in self:
            domain = [
                ('th_role_id', '=', line.th_role_id.id),
                ('th_user_id', '=', line.th_user_id.id),
                ('th_active', '=', True),
                ('id', '!=', line.id)
            ]
            
            # Check for overlapping periods
            if line.th_date_to:
                domain.extend([
                    '|', '|', '|',
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '>=', line.th_date_from),
                    '&', ('th_date_from', '<=', line.th_date_to), 
                         ('th_date_to', '>=', line.th_date_to),
                    '&', ('th_date_from', '>=', line.th_date_from), 
                         ('th_date_to', '<=', line.th_date_to),
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '=', False)
                ])
            else:
                domain.extend([
                    '|',
                    ('th_date_to', '=', False),
                    ('th_date_to', '>=', line.th_date_from)
                ])
            
            overlapping = self.search(domain)
            if overlapping:
                raise ValidationError(_(
                    'User %s đã có phân quyền trùng lặp cho role %s'
                ) % (line.th_user_id.name, line.th_role_id.name))

    @api.model
    def create(self, vals):
        line = super().create(vals)
        line._check_and_update_role_status()
        return line

    def write(self, vals):
        # Store previous state for tracking
        if 'th_is_enabled' in vals:
            for line in self:
                line.th_previous_is_enabled = line.th_is_enabled
        
        result = super().write(vals)
        
        # Check if we need to update role status
        if any(field in vals for field in ['th_date_from', 'th_date_to', 'th_active']):
            self._check_and_update_role_status()
        
        return result

    def unlink(self):
        # Disable roles before deletion
        self.filtered('th_is_enabled')._disable_role()
        return super().unlink()

    def _check_and_update_role_status(self):
        """Check and update role status based on current time"""
        now = fields.Datetime.now()
        
        for line in self:
            if not line.th_active:
                if line.th_is_enabled:
                    line._disable_role()
                continue
            
            should_be_enabled = (
                line.th_date_from <= now and
                (not line.th_date_to or line.th_date_to > now)
            )
            
            if should_be_enabled and not line.th_is_enabled:
                line._enable_role()
            elif not should_be_enabled and line.th_is_enabled:
                line._disable_role()

    def _enable_role(self):
        """Enable the role for the user"""
        self.ensure_one()
        if self.th_is_enabled:
            return

        try:
            # Get groups from the role
            groups_to_add = self.th_role_id.group_ids

            if groups_to_add:
                # Add groups to user with context to prevent recursion
                self.th_user_id.with_context(
                    th_enable_role_recursion_guard=True,
                    role_line_changed=True
                ).write({
                    'groups_id': [(4, group.id) for group in groups_to_add]
                })

                # Store applied groups
                self.th_applied_group_ids = [(6, 0, groups_to_add.ids)]

            # Update status
            self.th_is_enabled = True

            # Log history
            self._log_role_change('activate')

            # Send notification
            self._send_role_notification('activated')

            _logger.info(
                f"Role {self.th_role_id.name} activated for user {self.th_user_id.name}"
            )

        except Exception as e:
            _logger.error(
                f"Error activating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi kích hoạt role: %s'
            ) % str(e))

    def _disable_role(self):
        """Disable the role for the user"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Get groups that were applied
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Check if groups are used by other active roles
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Remove groups from user (only those not used by other roles)
                # Use context to prevent recursion in write() methods
                if groups_to_actually_remove:
                    self.th_user_id.with_context(
                        th_disable_role_recursion_guard=True,
                        role_line_changed=True
                    ).write({
                        'groups_id': [(3, group.id) for group in groups_to_actually_remove]
                    })

                # Clear applied groups
                self.th_applied_group_ids = [(5, 0, 0)]

            # Update status
            self.th_is_enabled = False

            # Log history
            self._log_role_change('deactivate')

            # Send notification
            self._send_role_notification('deactivated')

            _logger.info(
                f"Role {self.th_role_id.name} deactivated for user {self.th_user_id.name}"
            )

        except Exception as e:
            _logger.error(
                f"Error deactivating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _disable_role_safe_sql(self):
        """Alternative method using direct SQL to avoid recursion"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Get groups that were applied
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Check if groups are used by other active roles
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Remove groups using direct SQL to avoid triggers
                if groups_to_actually_remove:
                    for group in groups_to_actually_remove:
                        self.env.cr.execute("""
                            DELETE FROM res_groups_users_rel
                            WHERE uid = %s AND gid = %s
                        """, (self.th_user_id.id, group.id))

                # Clear applied groups
                self.th_applied_group_ids = [(5, 0, 0)]

            # Update status
            self.th_is_enabled = False

            # Log history
            self._log_role_change('deactivate')

            _logger.info(
                f"Role {self.th_role_id.name} deactivated for user {self.th_user_id.name} (SQL method)"
            )

        except Exception as e:
            _logger.error(
                f"Error deactivating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _log_role_change(self, action):
        """Log role change to history"""
        self.env['th.role.history'].create({
            'th_user_id': self.th_user_id.id,
            'th_role_id': self.th_role_id.id,
            'th_date': fields.Datetime.now(),
            'th_action': action,
            'th_performed_by': self.env.user.id,
            'th_role_line_id': self.id,
        })

    def _send_role_notification(self, action):
        """Send notification about role change"""
        template_name = f'th_time_based_user_role.role_{action}_notification'
        template = self.env.ref(template_name, raise_if_not_found=False)
        
        if template:
            template.send_mail(self.id, force_send=True)

    @api.model
    def th_update_role_status(self):
        """Cron job method to update all role statuses"""
        _logger.info("Starting automatic role status update")
        
        # Get all active role lines
        active_lines = self.search([('th_active', '=', True)])
        
        updated_count = 0
        for line in active_lines:
            old_status = line.th_is_enabled
            line._check_and_update_role_status()
            if old_status != line.th_is_enabled:
                updated_count += 1
        
        _logger.info(f"Role status update completed. {updated_count} roles updated.")
        return updated_count

    def action_enable_role(self):
        """Manual action to enable role"""
        self.ensure_one()
        if not self.th_is_enabled:
            self._enable_role()

    def action_disable_role(self):
        """Manual action to disable role"""
        self.ensure_one()
        if self.th_is_enabled:
            self._disable_role()

    def action_view_history(self):
        """Action to view role history"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_role_history').read()[0]
        action['domain'] = [('th_role_line_id', '=', self.id)]
        return action
