# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import logging

_logger = logging.getLogger(__name__)


class ThUserRoleLine(models.Model):
    _name = 'th.user.role.line'
    _description = 'Phân quyền Role người dùng theo thời gian'
    _order = 'th_date_from desc'
    _rec_name = 'display_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    th_role_id = fields.Many2one(
        'th.user.role',
        string='Role',
        required=True,
        tracking=True,
        help="Role cần gán"
    )

    th_user_id = fields.Many2one(
        'res.users',
        string='Người dùng',
        required=True,
        ondelete='cascade',
        tracking=True,
        help="User được gán role"
    )

    # New separate date and time fields
    th_start_date = fields.Date(
        string='<PERSON><PERSON><PERSON> bắt đầu',
        required=True,
        default=fields.Date.today,
        tracking=True,
        help="Ngày role trở nên hoạt động"
    )

    th_start_time = fields.Float(
        string='Giờ bắt đầu',
        required=True,
        default=8.0,  # 8:00 AM
        tracking=True,
        help="Thời gian bắt đầu trong ngày (định dạng 24h, ví dụ: 8.5 = 8:30)"
    )

    th_end_date = fields.Date(
        string='Ngày kết thúc',
        tracking=True,
        help="Ngày role hết hạn (tùy chọn)"
    )

    th_end_time = fields.Float(
        string='Giờ kết thúc',
        default=17.0,  # 5:00 PM
        tracking=True,
        help="Thời gian kết thúc trong ngày (định dạng 24h, ví dụ: 17.5 = 17:30)"
    )

    # Computed datetime fields for backward compatibility
    th_date_from = fields.Datetime(
        string='Ngày giờ bắt đầu',
        compute='_compute_datetime_fields',
        inverse='_inverse_date_from',
        store=True,
        tracking=True,
        help="Ngày và giờ role trở nên hoạt động (computed từ date + time)"
    )

    th_date_to = fields.Datetime(
        string='Ngày giờ kết thúc',
        compute='_compute_datetime_fields',
        inverse='_inverse_date_to',
        store=True,
        tracking=True,
        help="Ngày và giờ role hết hạn (computed từ date + time)"
    )

    th_is_enabled = fields.Boolean(
        string='Đang hoạt động',
        default=False,
        tracking=True,
        help="Role hiện tại có đang hoạt động cho user này không"
    )

    th_previous_is_enabled = fields.Boolean(
        string='Trạng thái trước đó',
        default=False,
        help="Trạng thái trước đó để theo dõi thay đổi"
    )

    th_applied_group_ids = fields.Many2many(
        'res.groups',
        'th_role_line_applied_group_rel',
        'line_id',
        'group_id',
        string='Nhóm đã áp dụng',
        help="Các group đã được áp dụng cho user với role này"
    )

    th_active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        tracking=True,
        help="Nếu bỏ chọn, phân quyền này sẽ bị bỏ qua"
    )

    th_notes = fields.Text(
        string='Ghi chú',
        help="Ghi chú bổ sung về phân quyền role này"
    )

    display_name = fields.Char(
        string='Tên hiển thị',
        compute='_compute_display_name',
        store=True
    )

    th_status = fields.Selection([
        ('pending', 'Chờ xử lý'),
        ('active', 'Hoạt động'),
        ('expired', 'Hết hạn'),
        ('disabled', 'Vô hiệu hóa')
    ], string='Trạng thái', compute='_compute_th_status', store=True)

    th_days_until_start = fields.Integer(
        string='Số ngày đến khi bắt đầu',
        compute='_compute_th_days_until_start',
        help="Số ngày cho đến khi role trở nên hoạt động"
    )

    th_days_until_end = fields.Integer(
        string='Số ngày đến khi kết thúc',
        compute='_compute_th_days_until_end',
        help="Số ngày cho đến khi role hết hạn"
    )

    # Recurrence fields
    th_is_recurring = fields.Boolean(
        string='Lặp lại',
        default=False,
        tracking=True,
        help="Cho phép tự động tạo role assignment mới sau khi kết thúc"
    )

    th_recurrence_type = fields.Selection([
        ('daily', 'Hằng ngày'),
        ('weekly', 'Hằng tuần'),
        ('monthly', 'Hằng tháng')
    ], string='Loại lặp lại', default='daily', tracking=True,
       help="Loại chu kỳ lặp lại")

    th_recurrence_interval = fields.Integer(
        string='Khoảng cách lặp lại',
        default=1,
        tracking=True,
        help="Khoảng cách giữa các lần lặp lại (ví dụ: mỗi 2 ngày, 2 tuần)"
    )

    th_recurrence_end_date = fields.Date(
        string='Ngày kết thúc lặp lại',
        tracking=True,
        help="Ngày kết thúc việc tạo role assignments mới"
    )

    th_recurrence_count = fields.Integer(
        string='Số lần lặp lại tối đa',
        default=0,
        tracking=True,
        help="Số lần lặp lại tối đa (0 = không giới hạn)"
    )

    th_parent_recurrence_id = fields.Many2one(
        'th.user.role.line',
        string='Role gốc',
        ondelete='cascade',
        help="Role assignment gốc tạo ra chuỗi lặp lại này"
    )

    th_recurrence_sequence = fields.Integer(
        string='Thứ tự lặp lại',
        default=0,
        help="Thứ tự trong chuỗi lặp lại (0 = gốc, 1 = lần đầu, ...)"
    )

    th_child_recurrence_ids = fields.One2many(
        'th.user.role.line',
        'th_parent_recurrence_id',
        string='Role assignments con',
        help="Các role assignments được tạo từ lặp lại"
    )

    th_recurrence_created_count = fields.Integer(
        string='Số lần đã tạo',
        compute='_compute_th_recurrence_created_count',
        store=True,
        help="Số role assignments đã được tạo từ lặp lại"
    )

    # Computed fields for conditional display logic
    th_show_end_date = fields.Boolean(
        string='Hiển thị ngày kết thúc',
        compute='_compute_display_logic',
        help="Xác định có hiển thị trường th_end_date không"
    )

    th_show_recurrence_end_date = fields.Boolean(
        string='Hiển thị ngày kết thúc lặp lại',
        compute='_compute_display_logic',
        help="Xác định có hiển thị trường th_recurrence_end_date không"
    )

    th_effective_end_date = fields.Date(
        string='Ngày kết thúc hiệu lực',
        compute='_compute_effective_dates',
        help="Ngày kết thúc thực tế được sử dụng cho logic recurrence"
    )

    th_recurrence_description = fields.Char(
        string='Mô tả lặp lại',
        compute='_compute_recurrence_description',
        help="Mô tả chi tiết về cách thức lặp lại"
    )

    @api.depends('th_start_date', 'th_start_time', 'th_end_date', 'th_end_time')
    def _compute_datetime_fields(self):
        """Compute datetime fields from separate date and time fields"""
        for line in self:
            # Compute th_date_from
            if line.th_start_date:
                start_datetime = datetime.combine(
                    line.th_start_date,
                    self._float_to_time(line.th_start_time or 0.0)
                )
                line.th_date_from = start_datetime
            else:
                line.th_date_from = False

            # Compute th_date_to
            if line.th_end_date:
                end_datetime = datetime.combine(
                    line.th_end_date,
                    self._float_to_time(line.th_end_time or 0.0)
                )
                line.th_date_to = end_datetime
            else:
                line.th_date_to = False

    def _inverse_date_from(self):
        """Đặt các trường date và time từ th_date_from"""
        for line in self:
            if line.th_date_from:
                line.th_start_date = line.th_date_from.date()
                line.th_start_time = self._time_to_float(line.th_date_from.time())

    def _inverse_date_to(self):
        """Đặt các trường date và time từ th_date_to"""
        for line in self:
            if line.th_date_to:
                line.th_end_date = line.th_date_to.date()
                line.th_end_time = self._time_to_float(line.th_date_to.time())

    def _float_to_time(self, float_time):
        """Chuyển đổi giá trị float thành đối tượng time"""
        hours = int(float_time)
        minutes = int((float_time - hours) * 60)
        return datetime.min.time().replace(hour=hours, minute=minutes)

    def _time_to_float(self, time_obj):
        """Chuyển đổi thời gian thành float"""
        return time_obj.hour + time_obj.minute / 60.0

    def _calculate_expected_end_date_from_count(self):
        """Tính toán ngày kết thúc dự kiến dựa trên số lần lặp lại"""
        self.ensure_one()

        if not self.th_is_recurring or not self.th_end_date or self.th_recurrence_count <= 0:
            return None

        try:
            current_date = self.th_end_date
            for i in range(self.th_recurrence_count):
                current_date = self._calculate_next_date(current_date)
                if not current_date:
                    break
            return current_date
        except Exception:
            return None

    @api.depends('th_is_recurring', 'th_parent_recurrence_id')
    def _compute_display_logic(self):
        """Compute Các trường nào sẽ được hiển thị dựa trên cài đặt lặp lại"""
        for line in self:
            # Child recurrence records không hiển thị các trường lặp lại
            if line.th_parent_recurrence_id:
                line.th_show_end_date = True
                line.th_show_recurrence_end_date = False
            elif line.th_is_recurring:
                # Vai trò định kỳ: Hiển thị cả hai trường để tránh nhầm lẫn
                line.th_show_end_date = True
                line.th_show_recurrence_end_date = True
            else:
                # Không lặp lại role chỉ hiển thị date
                line.th_show_end_date = True
                line.th_show_recurrence_end_date = False

    @api.depends('th_is_recurring', 'th_end_date', 'th_recurrence_end_date')
    def _compute_effective_dates(self):
        """Compute ngày kết thúc hiệu lực cho logic lặp lại"""
        for line in self:
            if line.th_is_recurring and line.th_recurrence_end_date:
                # Đối với các vai trò định kỳ, hãy sử dụng Recurrence_end_date làm giới hạn
                line.th_effective_end_date = line.th_recurrence_end_date
            else:
                # Đối với các vai trò tái diễn không định kỳ hoặc không giới hạn, hãy sử dụng end_date
                line.th_effective_end_date = line.th_end_date

    @api.depends('th_is_recurring', 'th_recurrence_type', 'th_recurrence_interval',
                 'th_recurrence_end_date', 'th_recurrence_count')
    def _compute_recurrence_description(self):
        """Compute mô tả lặp lại cho người dùng"""
        for line in self:
            if not line.th_is_recurring:
                line.th_recurrence_description = ""
                continue

            type_map = {
                'daily': 'ngày',
                'weekly': 'tuần',
                'monthly': 'tháng'
            }

            interval_text = f"mỗi {line.th_recurrence_interval} {type_map.get(line.th_recurrence_type, 'ngày')}"

            # Thêm điều kiện kết thúc
            end_conditions = []
            if line.th_recurrence_end_date:
                end_conditions.append(f"đến {line.th_recurrence_end_date.strftime('%d/%m/%Y')}")
            if line.th_recurrence_count > 0:
                end_conditions.append(f"tối đa {line.th_recurrence_count} lần")

            if end_conditions:
                line.th_recurrence_description = f"Lặp lại {interval_text}, {' và '.join(end_conditions)}"
            else:
                line.th_recurrence_description = f"Lặp lại {interval_text}, không giới hạn"

    @api.depends('th_child_recurrence_ids')
    def _compute_th_recurrence_created_count(self):
        for line in self:
            line.th_recurrence_created_count = len(line.th_child_recurrence_ids)

    @api.depends('th_role_id.name', 'th_user_id.name', 'th_start_date', 'th_start_time',
                 'th_end_date', 'th_end_time', 'th_recurrence_sequence')
    def _compute_display_name(self):
        for line in self:
            role_name = line.th_role_id.name or ''
            user_name = line.th_user_id.name or ''

            # Format date and time
            if line.th_start_date:
                date_from = line.th_start_date.strftime('%d/%m/%Y')
                time_from = f"{int(line.th_start_time):02d}:{int((line.th_start_time % 1) * 60):02d}"
                start_info = f"{date_from} {time_from}"
            else:
                start_info = ''

            if line.th_end_date:
                date_to = line.th_end_date.strftime('%d/%m/%Y')
                time_to = f"{int(line.th_end_time):02d}:{int((line.th_end_time % 1) * 60):02d}"
                end_info = f"{date_to} {time_to}"
            else:
                end_info = 'Không có ngày kết thúc'

            # Add hiển thị tên display name
            recurrence_info = ''
            if line.th_recurrence_sequence > 0:
                recurrence_info = f" [Lặp #{line.th_recurrence_sequence}]"
            elif line.th_is_recurring and line.th_recurrence_sequence == 0:
                recurrence_info = f" [Gốc - {line.th_recurrence_created_count} con]"

            line.display_name = f"{user_name} - {role_name} ({start_info} - {end_info}){recurrence_info}"

    @api.depends('th_is_enabled', 'th_active', 'th_date_from', 'th_date_to')
    def _compute_th_status(self):
        now = fields.Datetime.now()
        for line in self:
            if not line.th_active:
                line.th_status = 'disabled'
            elif not line.th_is_enabled and line.th_date_from > now:
                line.th_status = 'pending'
            elif line.th_is_enabled:
                line.th_status = 'active'
            elif line.th_date_to and line.th_date_to < now:
                line.th_status = 'expired'
            else:
                line.th_status = 'disabled'

    @api.depends('th_date_from')
    def _compute_th_days_until_start(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_from:
                delta = line.th_date_from - now
                line.th_days_until_start = delta.days
            else:
                line.th_days_until_start = 0

    @api.depends('th_date_to')
    def _compute_th_days_until_end(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_to:
                delta = line.th_date_to - now
                line.th_days_until_end = delta.days
            else:
                line.th_days_until_end = 999999  # No end date

    @api.constrains('th_start_date', 'th_start_time', 'th_end_date', 'th_end_time')
    def _check_dates(self):
        for line in self:
            # Kiểm tra xem ngày kết thúc/giờ là sau ngày/giờ bắt đầu
            if line.th_end_date and line.th_start_date:
                if line.th_end_date < line.th_start_date:
                    raise ValidationError(_('Ngày kết thúc phải sau ngày bắt đầu'))
                elif line.th_end_date == line.th_start_date:
                    # nếu cùng ngày kiểm tra giờ
                    if line.th_end_time <= line.th_start_time:
                        raise ValidationError(_('Giờ kết thúc phải sau giờ bắt đầu trong cùng ngày'))

            # Kiểm tra định dạng thời gian (0-24 giờ)
            if line.th_start_time < 0 or line.th_start_time >= 24:
                raise ValidationError(_('Giờ bắt đầu phải từ 0.0 đến 23.59'))
            if line.th_end_time < 0 or line.th_end_time >= 24:
                raise ValidationError(_('Giờ kết thúc phải từ 0.0 đến 23.59'))

    @api.constrains('th_role_id', 'th_user_id', 'th_date_from', 'th_date_to')
    def _check_overlapping_assignments(self):
        for line in self:
            domain = [
                ('th_role_id', '=', line.th_role_id.id),
                ('th_user_id', '=', line.th_user_id.id),
                ('th_active', '=', True),
                ('id', '!=', line.id)
            ]
            
            # Kiểm tra các khoản thời gian trùng lặp
            if line.th_date_to:
                domain.extend([
                    '|', '|', '|',
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '>=', line.th_date_from),
                    '&', ('th_date_from', '<=', line.th_date_to), 
                         ('th_date_to', '>=', line.th_date_to),
                    '&', ('th_date_from', '>=', line.th_date_from), 
                         ('th_date_to', '<=', line.th_date_to),
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '=', False)
                ])
            else:
                domain.extend([
                    '|',
                    ('th_date_to', '=', False),
                    ('th_date_to', '>=', line.th_date_from)
                ])
            
            overlapping = self.search(domain)
            if overlapping:
                raise ValidationError(_(
                    'User %s đã có phân quyền trùng lặp cho role %s'
                ) % (line.th_user_id.name, line.th_role_id.name))

    @api.constrains('th_is_recurring', 'th_recurrence_type', 'th_recurrence_interval',
                    'th_recurrence_end_date', 'th_recurrence_count', 'th_end_date')
    def _check_recurrence_settings(self):
        for line in self:
            if line.th_is_recurring:
                # Phải có ngày kết thúc để lặp lại
                if not line.th_end_date:
                    raise ValidationError(_(
                        'Role phải có ngày kết thúc để có thể lặp lại'
                    ))

                # Khoảng thời gin phải là số dương
                if line.th_recurrence_interval <= 0:
                    raise ValidationError(_(
                        'Khoảng cách lặp lại phải lớn hơn 0'
                    ))

                # Check ngày kết thúc lặp lại
                if line.th_recurrence_end_date:
                    # Ngày kết thúc lặp lại phải là sau ngày kết thúc vai trò
                    if line.th_recurrence_end_date <= line.th_end_date:
                        raise ValidationError(_(
                            'Ngày kết thúc lặp lại (%s) phải sau ngày kết thúc role đầu tiên (%s)'
                        ) % (line.th_recurrence_end_date.strftime('%d/%m/%Y'),
                             line.th_end_date.strftime('%d/%m/%Y')))

                # Đếm số ngày lặp lại không thể âm
                if line.th_recurrence_count < 0:
                    raise ValidationError(_(
                        'Số lần lặp lại không thể âm'
                    ))

                # Phải có ngày kết thúc hoặc giới hạn đếm (nhưng không nhất thiết là cả hai)
                if not line.th_recurrence_end_date and line.th_recurrence_count == 0:
                    raise ValidationError(_(
                        'Phải thiết lập ngày kết thúc lặp lại hoặc số lần lặp lại tối đa'
                    ))

                # Xác thực logic: Nếu cả hai trường được đặt, hãy đảm bảo chúng nhất quán
                if line.th_recurrence_end_date and line.th_recurrence_count > 0:
                    # Tính ngày kết thúc dự kiến dựa trên số lượng
                    expected_end = line._calculate_expected_end_date_from_count()
                    if expected_end and expected_end > line.th_recurrence_end_date:
                        raise ValidationError(_(
                            'Số lần lặp lại (%d) sẽ vượt quá ngày kết thúc lặp lại (%s). '
                            'Vui lòng điều chỉnh một trong hai giá trị.'
                        ) % (line.th_recurrence_count, line.th_recurrence_end_date.strftime('%d/%m/%Y')))

    @api.constrains('th_parent_recurrence_id')
    def _check_recurrence_hierarchy(self):
        for line in self:
            if line.th_parent_recurrence_id:
                # Child cannot be recurring
                if line.th_is_recurring:
                    raise ValidationError(_(
                        'Role con trong chuỗi lặp lại không thể có thiết lập lặp lại riêng'
                    ))

                # Ngăn chặn vòng lặp
                if line.th_parent_recurrence_id == line:
                    raise ValidationError(_(
                        'Role không thể là cha của chính nó'
                    ))

                # Kiểm tra lặp lại
                parent = line.th_parent_recurrence_id
                visited = set()
                while parent and parent.id not in visited:
                    visited.add(parent.id)
                    if parent.th_parent_recurrence_id == line:
                        raise ValidationError(_(
                            'Phát hiện vòng lặp trong chuỗi lặp lại'
                        ))
                    parent = parent.th_parent_recurrence_id

    @api.model
    def create(self, vals):
        line = super().create(vals)
        line._check_and_update_role_status()
        return line

    def write(self, vals):
        # Lưu trạng thái trước đó để theo dõi
        if 'th_is_enabled' in vals:
            for line in self:
                line.th_previous_is_enabled = line.th_is_enabled

        result = super().write(vals)

        # Kiểm tra xem có tạo vai trò hay không
        if any(field in vals for field in ['th_date_from', 'th_date_to', 'th_active']):
            self._check_and_update_role_status()

        # Quá trình tái phát nếu trạng thái vai trò thay đổi thành bị vô hiệu hóa do hết hạn
        if 'th_is_enabled' in vals and not vals['th_is_enabled']:
            for line in self:
                if line.th_is_recurring and not line.th_parent_recurrence_id:
                    # Kiểm tra xem vai trò kết thúc một cách tự nhiên (không bị vô hiệu hóa thủ công)
                    now = fields.Datetime.now()
                    if line.th_date_to and line.th_date_to <= now:
                        # Delay lặp lại quá trình để tránh xung đột
                        line.with_delay(eta=60)._process_recurrence()

        return result

    def unlink(self):
        # Tắt vai trò trước khi xóa
        if self.filtered('th_is_enabled'):
            self.filtered('th_is_enabled')._disable_role()
        return super().unlink()

    def _check_and_update_role_status(self):
        """Kiểm tra và cập nhật trạng thái vai trò dựa trên thời gian hiện tại"""
        now = fields.Datetime.now()
        
        for line in self:
            if not line.th_active:
                if line.th_is_enabled:
                    line._disable_role()
                continue
            
            should_be_enabled = (
                line.th_date_from <= now and
                (not line.th_date_to or line.th_date_to > now)
            )
            
            if should_be_enabled and not line.th_is_enabled:
                line._enable_role()
            elif not should_be_enabled and line.th_is_enabled:
                line._disable_role()

    def _enable_role(self):
        """Enable vai trò cho người dùng"""
        self.ensure_one()
        if self.th_is_enabled:
            return

        try:
            # Get groups vai trò
            groups_to_add = self.th_role_id.group_ids

            if groups_to_add:
                # Thêm nhóm vào người dùng có context để ngăn chặn đệ quy trong hàm write ở res_users
                self.th_user_id.with_context(
                    th_enable_role_recursion_guard=True,
                    role_line_changed=True
                ).write({
                    'groups_id': [(4, group.id) for group in groups_to_add]
                })

                # Lưu các group đã áp dụng
                self.th_applied_group_ids = [(6, 0, groups_to_add.ids)]

            # Update status
            self.th_is_enabled = True

            # Log history
            self._log_role_change('activate')

            # Send notification
            self._send_role_notification('activated')

        except Exception as e:
            raise UserError(_(
                'Lỗi khi kích hoạt role: %s'
            ) % str(e))

    def _disable_role(self):
        """Tắt vai trò cho người dùng"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Get groups that were applied
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Check if groups are used by other active roles
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Xóa các nhóm khỏi người dùng (chỉ những người không được sử dụng bởi các vai trò khác)
                #Sử dụng bối cảnh để ngăn chặn đệ quy trong các phương thức write ở res_users
                if groups_to_actually_remove:
                    self.th_user_id.with_context(
                        th_disable_role_recursion_guard=True,
                        role_line_changed=True
                    ).write({
                        'groups_id': [(3, group.id) for group in groups_to_actually_remove]
                    })

                # Clear applied groups
                self.th_applied_group_ids = [(5, 0, 0)]

            # Update status
            self.th_is_enabled = False

            # Log history
            self._log_role_change('deactivate')

            # Send notification
            self._send_role_notification('deactivated')

        except Exception as e:
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _disable_role_safe_sql(self):
        """Phương pháp thay thế disable sử dụng sql"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Lấy tất cả các vai trò
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Kiểm tra xem các group có đang được sử dụng bởi các vai trò khác không
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Xóa các group không còn sử dụng
                if groups_to_actually_remove:
                    for group in groups_to_actually_remove:
                        self.env.cr.execute("""
                            DELETE FROM res_groups_users_rel
                            WHERE uid = %s AND gid = %s
                        """, (self.th_user_id.id, group.id))

                # Clear các group đã áp dụng
                self.th_applied_group_ids = [(5, 0, 0)]

            # Cập nhật trạng thái
            self.th_is_enabled = False

            # Tạo lịch sử
            self._log_role_change('deactivate')

        except Exception as e:
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _log_role_change(self, action):
        """Thay đổi vai trò vào lịch sử"""
        self.env['th.role.history'].create({
            'th_user_id': self.th_user_id.id,
            'th_role_id': self.th_role_id.id,
            'th_date': fields.Datetime.now(),
            'th_action': action,
            'th_performed_by': self.env.user.id,
            'th_role_line_id': self.id,
        })

    def _send_role_notification(self, action):
        """Gửi thông báo về thay đổi vai trò"""
        template_name = f'th_time_based_user_role.role_{action}_notification'
        template = self.env.ref(template_name, raise_if_not_found=False)
        
        if template:
            template.send_mail(self.id, force_send=True)

    @api.model
    def th_update_role_status(self):
        """Cron job method cho cập nhật tất cả các vai trò"""
        # Lấy tất cả các vai trò đang hoạt động
        active_lines = self.search([('th_active', '=', True)])
        
        updated_count = 0
        for line in active_lines:
            old_status = line.th_is_enabled
            line._check_and_update_role_status()
            if old_status != line.th_is_enabled:
                updated_count += 1
        
        return updated_count

    def action_enable_role(self):
        """Hành động thủ công để kích hoạt vai trò"""
        self.ensure_one()
        if not self.th_is_enabled:
            self._enable_role()

    def action_disable_role(self):
        """Hành động thủ công để bỏ kích hoạt vai trò"""
        self.ensure_one()
        if self.th_is_enabled:
            self._disable_role()

    def action_view_history(self):
        """Action hiển thị view lịch sử"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_role_history').read()[0]
        action['domain'] = [('th_role_line_id', '=', self.id)]
        return action

    # ==================== RECURRENCE METHODS ====================

    def _calculate_next_date(self, base_date):
        """Tính ngày tiếp theo dựa trên loại lặp lại và khoảng thời gian"""
        self.ensure_one()

        if not base_date:
            return None

        if self.th_recurrence_type == 'daily':
            return base_date + timedelta(days=self.th_recurrence_interval)
        elif self.th_recurrence_type == 'weekly':
            return base_date + timedelta(weeks=self.th_recurrence_interval)
        elif self.th_recurrence_type == 'monthly':
            return base_date + relativedelta(months=self.th_recurrence_interval)

        return None

    def _should_create_recurrence(self):
        """Kiểm tra xem có tạo sự lặp lại tiếp theo không"""
        self.ensure_one()

        # Phải định kỳ và có ngày kết thúc
        if not self.th_is_recurring or not self.th_end_date:
            return False

        # Kiểm tra xem đã đạt giới hạn số lần lặp lại chưa
        if self.th_recurrence_count > 0:
            created_count = self.th_recurrence_created_count
            if created_count >= self.th_recurrence_count:
                return False

        # Kiểm tra xem đã đạt giới hạn ngày kết thúc lặp lại chưa
        if self.th_recurrence_end_date:
            next_date = self._calculate_next_date(self.th_end_date)
            if next_date and next_date > self.th_recurrence_end_date:
                return False

        return True

    def _get_recurrence_limit_date(self):
        """Lấy ngày giới hạn cho sự lặp lại"""
        self.ensure_one()

        if self.th_recurrence_end_date:
            return self.th_recurrence_end_date
        elif self.th_recurrence_count > 0:
            # Tính toán ngày kết thúc dựa trên số lần lặp lại
            return self._calculate_expected_end_date_from_count()
        else:
            return None

    def _create_next_recurrence(self):
        """Tạo mới role line cho sự lặp lại tiếp theo"""
        self.ensure_one()

        if not self._should_create_recurrence():
            return None

        # Tính toán ngày bắt đầu tiếp theo
        next_start_date = self._calculate_next_date(self.th_end_date)
        if not next_start_date:
            return None

        # Tính ngày kết thúc tiếp theo
        if self.th_end_date and self.th_start_date:
            # Tính thời lượng theo ngày
            duration_days = (self.th_end_date - self.th_start_date).days
            next_end_date = next_start_date + timedelta(days=duration_days)
        else:
            next_end_date = next_start_date

        # Xác định trình tự và role assignment gốc
        parent_id = self.th_parent_recurrence_id.id if self.th_parent_recurrence_id else self.id
        sequence = self.th_recurrence_sequence + 1

        # Tạo dòng vai trò mới bằng cách sử dụng trường ngày+thời gian
        new_vals = {
            'th_role_id': self.th_role_id.id,
            'th_user_id': self.th_user_id.id,
            'th_start_date': next_start_date,
            'th_start_time': self.th_start_time,
            'th_end_date': next_end_date,
            'th_end_time': self.th_end_time,
            'th_active': self.th_active,
            'th_notes': f"Tự động tạo từ lặp lại #{sequence}",
            'th_parent_recurrence_id': parent_id,
            'th_recurrence_sequence': sequence,
            'th_is_recurring': False,
        }

        try:
            new_line = self.sudo().create(new_vals)
            return new_line
        except Exception as e:
            raise UserError(_('Không thể tạo lặp lại. Kiểm tra các điều kiện lặp lại.'))

    def _process_recurrence(self):
        """Quá trình lặp lại khi role kết thúc"""
        self.ensure_one()

        # Chỉ có quy trình cho các vai trò lặp lại gốc 
        if not self.th_is_recurring or self.th_parent_recurrence_id:
            return

        # Kiểm tra xem vai trò có thực sự kết thúc không
        now = fields.Datetime.now()
        if not self.th_date_to or self.th_date_to > now:
            return

        # Kiểm tra xem chúng ta có nên tạo lặp lại tiếp theo dựa trên các giới hạn không
        if not self._should_create_recurrence():
            return

        # Tạo mới lặp lại tiếp
        new_recurrence = self._create_next_recurrence()
        if new_recurrence:
            _logger.info(
                f"Automatic recurrence created for role {self.th_role_id.name} "
                f"for user {self.th_user_id.name}"
            )

    @api.model
    def th_process_all_recurrences(self):
        """Method cron để xử lý tất cả các đợt lặp lại đang chờ xử lý"""
        _logger.info("Starting recurrence processing")

        # Tìm tới vai trò đã kết thúc và cần lặ lại
        now = fields.Datetime.now()
        ended_recurring_roles = self.search([
            ('th_is_recurring', '=', True),
            ('th_parent_recurrence_id', '=', False),  # Phải là vài trò gốc nhé :))
            ('th_date_to', '<=', now),
            ('th_date_to', '!=', False),
        ])

        processed_count = 0
        for role_line in ended_recurring_roles:
            # Kiểm tra xem chúng ta đã tạo lặp lại cho vai trò này chưa
            existing_recurrence = self.search([
                ('th_parent_recurrence_id', '=', role_line.id),
                ('th_date_from', '>', role_line.th_date_to),
            ], limit=1)

            if not existing_recurrence and role_line._should_create_recurrence():
                new_recurrence = role_line._create_next_recurrence()
                if new_recurrence:
                    processed_count += 1

        return processed_count

    def action_th_view_recurrence_chain(self):
        """Action để xem tất cả các vai trò trong chuỗi lặp lại"""
        self.ensure_one()

        # Lấy vait trò lặp lại gốc
        root_id = self.th_parent_recurrence_id.id if self.th_parent_recurrence_id else self.id

        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['name'] = 'Chuỗi lặp lại Role'
        action['domain'] = [
            '|',
            ('id', '=', root_id),
            ('th_parent_recurrence_id', '=', root_id)
        ]
        action['context'] = {
            'search_default_group_start_date': 1,
        }
        return action

    def action_th_create_manual_recurrence(self):
        """Tạo lặp lại tiếp theo bằng cách thủ công"""
        self.ensure_one()

        if not self.th_is_recurring:
            raise UserError(_('Role này không được thiết lập để lặp lại'))

        if self.th_parent_recurrence_id:
            raise UserError(_('Chỉ có thể tạo lặp lại từ role gốc'))

        if not self.th_date_to:
            raise UserError(_('Role phải có ngày kết thúc để tạo lặp lại'))

        new_recurrence = self._create_next_recurrence()
        if new_recurrence:
            return {
                'name': _('Role lặp lại đã tạo'),
                'type': 'ir.actions.act_window',
                'res_model': 'th.user.role.line',
                'res_id': new_recurrence.id,
                'view_mode': 'form',
                'target': 'current',
            }
        else:
            raise UserError(_('Không thể tạo lặp lại. Kiểm tra các điều kiện lặp lại.'))

    def action_th_stop_recurrence(self):
        """Dừng lặp lại cho chuỗi role"""
        self.ensure_one()

        # Lấy vai trò gốc
        root_role = self.th_parent_recurrence_id if self.th_parent_recurrence_id else self

        # Dừng lặp lại trên vai trò gốc
        root_role.write({'th_is_recurring': False})

        now = fields.Datetime.now()
        future_recurrences = self.search([
            ('th_parent_recurrence_id', '=', root_role.id),
            ('th_date_from', '>', now),
            ('th_is_enabled', '=', False),
        ])

        if future_recurrences:
            future_recurrences.write({'th_active': False})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Đã dừng lặp lại'),
                'message': _('Đã dừng lặp lại và hủy %d role assignments tương lai') % len(future_recurrences),
                'type': 'success',
            }
        }
