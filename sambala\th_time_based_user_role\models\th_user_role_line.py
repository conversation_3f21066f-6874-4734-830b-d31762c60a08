# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import logging

_logger = logging.getLogger(__name__)


class ThUserRoleLine(models.Model):
    _name = 'th.user.role.line'
    _description = 'Phân quyền Role người dùng theo thời gian'
    _order = 'th_date_from desc'
    _rec_name = 'display_name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    th_role_id = fields.Many2one(
        'th.user.role',
        string='Role',
        required=True,
        tracking=True,
        help="Role cần gán"
    )

    th_user_id = fields.Many2one(
        'res.users',
        string='Người dùng',
        required=True,
        ondelete='cascade',
        tracking=True,
        help="User được gán role"
    )

    # New separate date and time fields
    th_start_date = fields.Date(
        string='<PERSON><PERSON><PERSON> bắt đầu',
        required=True,
        default=fields.Date.today,
        tracking=True,
        help="Ngày role trở nên hoạt động"
    )

    th_start_time = fields.Float(
        string='Giờ bắt đầu',
        required=True,
        default=8.0,  # 8:00 AM
        tracking=True,
        help="Thời gian bắt đầu trong ngày (định dạng 24h, ví dụ: 8.5 = 8:30)"
    )

    th_end_date = fields.Date(
        string='Ngày kết thúc',
        tracking=True,
        help="Ngày role hết hạn (tùy chọn)"
    )

    th_end_time = fields.Float(
        string='Giờ kết thúc',
        default=17.0,  # 5:00 PM
        tracking=True,
        help="Thời gian kết thúc trong ngày (định dạng 24h, ví dụ: 17.5 = 17:30)"
    )

    # Computed datetime fields for backward compatibility
    th_date_from = fields.Datetime(
        string='Ngày giờ bắt đầu',
        compute='_compute_datetime_fields',
        inverse='_inverse_date_from',
        store=True,
        required=True,
        tracking=True,
        help="Ngày và giờ role trở nên hoạt động (computed từ date + time)"
    )

    th_date_to = fields.Datetime(
        string='Ngày giờ kết thúc',
        compute='_compute_datetime_fields',
        inverse='_inverse_date_to',
        store=True,
        tracking=True,
        help="Ngày và giờ role hết hạn (computed từ date + time)"
    )

    th_is_enabled = fields.Boolean(
        string='Đang hoạt động',
        default=False,
        tracking=True,
        help="Role hiện tại có đang hoạt động cho user này không"
    )

    th_previous_is_enabled = fields.Boolean(
        string='Trạng thái trước đó',
        default=False,
        help="Trạng thái trước đó để theo dõi thay đổi"
    )

    th_applied_group_ids = fields.Many2many(
        'res.groups',
        'th_role_line_applied_group_rel',
        'line_id',
        'group_id',
        string='Nhóm đã áp dụng',
        help="Các group đã được áp dụng cho user với role này"
    )

    th_active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        tracking=True,
        help="Nếu bỏ chọn, phân quyền này sẽ bị bỏ qua"
    )

    th_notes = fields.Text(
        string='Ghi chú',
        help="Ghi chú bổ sung về phân quyền role này"
    )

    display_name = fields.Char(
        string='Tên hiển thị',
        compute='_compute_display_name',
        store=True
    )

    th_status = fields.Selection([
        ('pending', 'Chờ xử lý'),
        ('active', 'Hoạt động'),
        ('expired', 'Hết hạn'),
        ('disabled', 'Vô hiệu hóa')
    ], string='Trạng thái', compute='_compute_th_status', store=True)

    th_days_until_start = fields.Integer(
        string='Số ngày đến khi bắt đầu',
        compute='_compute_th_days_until_start',
        help="Số ngày cho đến khi role trở nên hoạt động"
    )

    th_days_until_end = fields.Integer(
        string='Số ngày đến khi kết thúc',
        compute='_compute_th_days_until_end',
        help="Số ngày cho đến khi role hết hạn"
    )

    # Recurrence fields
    th_is_recurring = fields.Boolean(
        string='Lặp lại',
        default=False,
        tracking=True,
        help="Cho phép tự động tạo role assignment mới sau khi kết thúc"
    )

    th_recurrence_type = fields.Selection([
        ('daily', 'Hằng ngày'),
        ('weekly', 'Hằng tuần'),
        ('monthly', 'Hằng tháng')
    ], string='Loại lặp lại', default='daily', tracking=True,
       help="Loại chu kỳ lặp lại")

    th_recurrence_interval = fields.Integer(
        string='Khoảng cách lặp lại',
        default=1,
        tracking=True,
        help="Khoảng cách giữa các lần lặp lại (ví dụ: mỗi 2 ngày, 2 tuần)"
    )

    th_recurrence_end_date = fields.Date(
        string='Ngày kết thúc lặp lại',
        tracking=True,
        help="Ngày kết thúc việc tạo role assignments mới"
    )

    th_recurrence_count = fields.Integer(
        string='Số lần lặp lại tối đa',
        default=0,
        tracking=True,
        help="Số lần lặp lại tối đa (0 = không giới hạn)"
    )

    th_parent_recurrence_id = fields.Many2one(
        'th.user.role.line',
        string='Role gốc',
        ondelete='cascade',
        help="Role assignment gốc tạo ra chuỗi lặp lại này"
    )

    th_recurrence_sequence = fields.Integer(
        string='Thứ tự lặp lại',
        default=0,
        help="Thứ tự trong chuỗi lặp lại (0 = gốc, 1 = lần đầu, ...)"
    )

    th_child_recurrence_ids = fields.One2many(
        'th.user.role.line',
        'th_parent_recurrence_id',
        string='Role assignments con',
        help="Các role assignments được tạo từ lặp lại"
    )

    th_recurrence_created_count = fields.Integer(
        string='Số lần đã tạo',
        compute='_compute_th_recurrence_created_count',
        store=True,
        help="Số role assignments đã được tạo từ lặp lại"
    )

    @api.depends('th_start_date', 'th_start_time', 'th_end_date', 'th_end_time')
    def _compute_datetime_fields(self):
        """Compute datetime fields from separate date and time fields"""
        for line in self:
            # Compute th_date_from
            if line.th_start_date:
                start_datetime = datetime.combine(
                    line.th_start_date,
                    self._float_to_time(line.th_start_time or 0.0)
                )
                line.th_date_from = start_datetime
            else:
                line.th_date_from = False

            # Compute th_date_to
            if line.th_end_date:
                end_datetime = datetime.combine(
                    line.th_end_date,
                    self._float_to_time(line.th_end_time or 0.0)
                )
                line.th_date_to = end_datetime
            else:
                line.th_date_to = False

    def _inverse_date_from(self):
        """Set date and time fields from th_date_from"""
        for line in self:
            if line.th_date_from:
                line.th_start_date = line.th_date_from.date()
                line.th_start_time = self._time_to_float(line.th_date_from.time())

    def _inverse_date_to(self):
        """Set date and time fields from th_date_to"""
        for line in self:
            if line.th_date_to:
                line.th_end_date = line.th_date_to.date()
                line.th_end_time = self._time_to_float(line.th_date_to.time())

    def _float_to_time(self, float_time):
        """Convert float time to time object"""
        hours = int(float_time)
        minutes = int((float_time - hours) * 60)
        return datetime.min.time().replace(hour=hours, minute=minutes)

    def _time_to_float(self, time_obj):
        """Convert time object to float"""
        return time_obj.hour + time_obj.minute / 60.0

    @api.depends('th_child_recurrence_ids')
    def _compute_th_recurrence_created_count(self):
        for line in self:
            line.th_recurrence_created_count = len(line.th_child_recurrence_ids)

    @api.depends('th_role_id.name', 'th_user_id.name', 'th_start_date', 'th_start_time',
                 'th_end_date', 'th_end_time', 'th_recurrence_sequence')
    def _compute_display_name(self):
        for line in self:
            role_name = line.th_role_id.name or ''
            user_name = line.th_user_id.name or ''

            # Format date and time
            if line.th_start_date:
                date_from = line.th_start_date.strftime('%d/%m/%Y')
                time_from = f"{int(line.th_start_time):02d}:{int((line.th_start_time % 1) * 60):02d}"
                start_info = f"{date_from} {time_from}"
            else:
                start_info = ''

            if line.th_end_date:
                date_to = line.th_end_date.strftime('%d/%m/%Y')
                time_to = f"{int(line.th_end_time):02d}:{int((line.th_end_time % 1) * 60):02d}"
                end_info = f"{date_to} {time_to}"
            else:
                end_info = 'Không có ngày kết thúc'

            # Add recurrence info to display name
            recurrence_info = ''
            if line.th_recurrence_sequence > 0:
                recurrence_info = f" [Lặp #{line.th_recurrence_sequence}]"
            elif line.th_is_recurring and line.th_recurrence_sequence == 0:
                recurrence_info = f" [Gốc - {line.th_recurrence_created_count} con]"

            line.display_name = f"{user_name} - {role_name} ({start_info} - {end_info}){recurrence_info}"

    @api.depends('th_is_enabled', 'th_active', 'th_date_from', 'th_date_to')
    def _compute_th_status(self):
        now = fields.Datetime.now()
        for line in self:
            if not line.th_active:
                line.th_status = 'disabled'
            elif not line.th_is_enabled and line.th_date_from > now:
                line.th_status = 'pending'
            elif line.th_is_enabled:
                line.th_status = 'active'
            elif line.th_date_to and line.th_date_to < now:
                line.th_status = 'expired'
            else:
                line.th_status = 'disabled'

    @api.depends('th_date_from')
    def _compute_th_days_until_start(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_from:
                delta = line.th_date_from - now
                line.th_days_until_start = delta.days
            else:
                line.th_days_until_start = 0

    @api.depends('th_date_to')
    def _compute_th_days_until_end(self):
        now = fields.Datetime.now()
        for line in self:
            if line.th_date_to:
                delta = line.th_date_to - now
                line.th_days_until_end = delta.days
            else:
                line.th_days_until_end = 999999  # No end date

    @api.constrains('th_start_date', 'th_start_time', 'th_end_date', 'th_end_time')
    def _check_dates(self):
        for line in self:
            # Check if end date/time is after start date/time
            if line.th_end_date and line.th_start_date:
                if line.th_end_date < line.th_start_date:
                    raise ValidationError(_('Ngày kết thúc phải sau ngày bắt đầu'))
                elif line.th_end_date == line.th_start_date:
                    # Same day, check time
                    if line.th_end_time <= line.th_start_time:
                        raise ValidationError(_('Giờ kết thúc phải sau giờ bắt đầu trong cùng ngày'))

            # Check time format (0-24 hours)
            if line.th_start_time < 0 or line.th_start_time >= 24:
                raise ValidationError(_('Giờ bắt đầu phải từ 0.0 đến 23.59'))
            if line.th_end_time < 0 or line.th_end_time >= 24:
                raise ValidationError(_('Giờ kết thúc phải từ 0.0 đến 23.59'))

    @api.constrains('th_role_id', 'th_user_id', 'th_date_from', 'th_date_to')
    def _check_overlapping_assignments(self):
        for line in self:
            domain = [
                ('th_role_id', '=', line.th_role_id.id),
                ('th_user_id', '=', line.th_user_id.id),
                ('th_active', '=', True),
                ('id', '!=', line.id)
            ]
            
            # Check for overlapping periods
            if line.th_date_to:
                domain.extend([
                    '|', '|', '|',
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '>=', line.th_date_from),
                    '&', ('th_date_from', '<=', line.th_date_to), 
                         ('th_date_to', '>=', line.th_date_to),
                    '&', ('th_date_from', '>=', line.th_date_from), 
                         ('th_date_to', '<=', line.th_date_to),
                    '&', ('th_date_from', '<=', line.th_date_from), 
                         ('th_date_to', '=', False)
                ])
            else:
                domain.extend([
                    '|',
                    ('th_date_to', '=', False),
                    ('th_date_to', '>=', line.th_date_from)
                ])
            
            overlapping = self.search(domain)
            if overlapping:
                raise ValidationError(_(
                    'User %s đã có phân quyền trùng lặp cho role %s'
                ) % (line.th_user_id.name, line.th_role_id.name))

    @api.constrains('th_is_recurring', 'th_recurrence_type', 'th_recurrence_interval',
                    'th_recurrence_end_date', 'th_recurrence_count', 'th_end_date')
    def _check_recurrence_settings(self):
        for line in self:
            if line.th_is_recurring:
                # Must have end date for recurrence
                if not line.th_end_date:
                    raise ValidationError(_(
                        'Role phải có ngày kết thúc để có thể lặp lại'
                    ))

                # Interval must be positive
                if line.th_recurrence_interval <= 0:
                    raise ValidationError(_(
                        'Khoảng cách lặp lại phải lớn hơn 0'
                    ))

                # End date must be after role end date
                if line.th_recurrence_end_date and line.th_recurrence_end_date <= line.th_end_date:
                    raise ValidationError(_(
                        'Ngày kết thúc lặp lại phải sau ngày kết thúc role'
                    ))

                # Count must be non-negative
                if line.th_recurrence_count < 0:
                    raise ValidationError(_(
                        'Số lần lặp lại không thể âm'
                    ))

                # Must have either end date or count limit
                if not line.th_recurrence_end_date and line.th_recurrence_count == 0:
                    raise ValidationError(_(
                        'Phải thiết lập ngày kết thúc lặp lại hoặc số lần lặp lại tối đa'
                    ))

    @api.constrains('th_parent_recurrence_id')
    def _check_recurrence_hierarchy(self):
        for line in self:
            if line.th_parent_recurrence_id:
                # Child cannot be recurring
                if line.th_is_recurring:
                    raise ValidationError(_(
                        'Role con trong chuỗi lặp lại không thể có thiết lập lặp lại riêng'
                    ))

                # Prevent circular reference
                if line.th_parent_recurrence_id == line:
                    raise ValidationError(_(
                        'Role không thể là cha của chính nó'
                    ))

                # Kiểm tra lặp lại
                parent = line.th_parent_recurrence_id
                visited = set()
                while parent and parent.id not in visited:
                    visited.add(parent.id)
                    if parent.th_parent_recurrence_id == line:
                        raise ValidationError(_(
                            'Phát hiện vòng lặp trong chuỗi lặp lại'
                        ))
                    parent = parent.th_parent_recurrence_id

    @api.model
    def create(self, vals):
        line = super().create(vals)
        line._check_and_update_role_status()
        return line

    def write(self, vals):
        # Store previous state for tracking
        if 'th_is_enabled' in vals:
            for line in self:
                line.th_previous_is_enabled = line.th_is_enabled

        result = super().write(vals)

        # Check if we need to update role status
        if any(field in vals for field in ['th_date_from', 'th_date_to', 'th_active']):
            self._check_and_update_role_status()

        # Process recurrence if role status changed to disabled due to expiration
        if 'th_is_enabled' in vals and not vals['th_is_enabled']:
            for line in self:
                if line.th_is_recurring and not line.th_parent_recurrence_id:
                    # Check if role ended naturally (not manually disabled)
                    now = fields.Datetime.now()
                    if line.th_date_to and line.th_date_to <= now:
                        # Delay recurrence processing to avoid conflicts
                        line.with_delay(eta=60)._process_recurrence()

        return result

    def unlink(self):
        # Disable roles before deletion
        self.filtered('th_is_enabled')._disable_role()
        return super().unlink()

    def _check_and_update_role_status(self):
        """Check and update role status based on current time"""
        now = fields.Datetime.now()
        
        for line in self:
            if not line.th_active:
                if line.th_is_enabled:
                    line._disable_role()
                continue
            
            should_be_enabled = (
                line.th_date_from <= now and
                (not line.th_date_to or line.th_date_to > now)
            )
            
            if should_be_enabled and not line.th_is_enabled:
                line._enable_role()
            elif not should_be_enabled and line.th_is_enabled:
                line._disable_role()

    def _enable_role(self):
        """Enable the role for the user"""
        self.ensure_one()
        if self.th_is_enabled:
            return

        try:
            # Get groups from the role
            groups_to_add = self.th_role_id.group_ids

            if groups_to_add:
                # Add groups to user with context to prevent recursion
                self.th_user_id.with_context(
                    th_enable_role_recursion_guard=True,
                    role_line_changed=True
                ).write({
                    'groups_id': [(4, group.id) for group in groups_to_add]
                })

                # Store applied groups
                self.th_applied_group_ids = [(6, 0, groups_to_add.ids)]

            # Update status
            self.th_is_enabled = True

            # Log history
            self._log_role_change('activate')

            # Send notification
            self._send_role_notification('activated')

            _logger.info(
                f"Role {self.th_role_id.name} activated for user {self.th_user_id.name}"
            )

        except Exception as e:
            _logger.error(
                f"Error activating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi kích hoạt role: %s'
            ) % str(e))

    def _disable_role(self):
        """Disable the role for the user"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Get groups that were applied
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Check if groups are used by other active roles
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Remove groups from user (only those not used by other roles)
                # Use context to prevent recursion in write() methods
                if groups_to_actually_remove:
                    self.th_user_id.with_context(
                        th_disable_role_recursion_guard=True,
                        role_line_changed=True
                    ).write({
                        'groups_id': [(3, group.id) for group in groups_to_actually_remove]
                    })

                # Clear applied groups
                self.th_applied_group_ids = [(5, 0, 0)]

            # Update status
            self.th_is_enabled = False

            # Log history
            self._log_role_change('deactivate')

            # Send notification
            self._send_role_notification('deactivated')

            _logger.info(
                f"Role {self.th_role_id.name} deactivated for user {self.th_user_id.name}"
            )

        except Exception as e:
            _logger.error(
                f"Error deactivating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _disable_role_safe_sql(self):
        """Alternative method using direct SQL to avoid recursion"""
        self.ensure_one()
        if not self.th_is_enabled:
            return

        try:
            # Get groups that were applied
            groups_to_remove = self.th_applied_group_ids

            if groups_to_remove:
                # Check if groups are used by other active roles
                other_active_roles = self.search([
                    ('th_user_id', '=', self.th_user_id.id),
                    ('th_is_enabled', '=', True),
                    ('id', '!=', self.id)
                ])

                groups_to_keep = other_active_roles.mapped('th_applied_group_ids')
                groups_to_actually_remove = groups_to_remove - groups_to_keep

                # Remove groups using direct SQL to avoid triggers
                if groups_to_actually_remove:
                    for group in groups_to_actually_remove:
                        self.env.cr.execute("""
                            DELETE FROM res_groups_users_rel
                            WHERE uid = %s AND gid = %s
                        """, (self.th_user_id.id, group.id))

                # Clear applied groups
                self.th_applied_group_ids = [(5, 0, 0)]

            # Update status
            self.th_is_enabled = False

            # Log history
            self._log_role_change('deactivate')

            _logger.info(
                f"Role {self.th_role_id.name} deactivated for user {self.th_user_id.name} (SQL method)"
            )

        except Exception as e:
            _logger.error(
                f"Error deactivating role {self.th_role_id.name} for user {self.th_user_id.name}: {e}"
            )
            raise UserError(_(
                'Lỗi khi vô hiệu hóa role: %s'
            ) % str(e))

    def _log_role_change(self, action):
        """Log role change to history"""
        self.env['th.role.history'].create({
            'th_user_id': self.th_user_id.id,
            'th_role_id': self.th_role_id.id,
            'th_date': fields.Datetime.now(),
            'th_action': action,
            'th_performed_by': self.env.user.id,
            'th_role_line_id': self.id,
        })

    def _send_role_notification(self, action):
        """Send notification about role change"""
        template_name = f'th_time_based_user_role.role_{action}_notification'
        template = self.env.ref(template_name, raise_if_not_found=False)
        
        if template:
            template.send_mail(self.id, force_send=True)

    @api.model
    def th_update_role_status(self):
        """Cron job method to update all role statuses"""
        _logger.info("Starting automatic role status update")
        
        # Get all active role lines
        active_lines = self.search([('th_active', '=', True)])
        
        updated_count = 0
        for line in active_lines:
            old_status = line.th_is_enabled
            line._check_and_update_role_status()
            if old_status != line.th_is_enabled:
                updated_count += 1
        
        _logger.info(f"Role status update completed. {updated_count} roles updated.")
        return updated_count

    def action_enable_role(self):
        """Manual action to enable role"""
        self.ensure_one()
        if not self.th_is_enabled:
            self._enable_role()

    def action_disable_role(self):
        """Manual action to disable role"""
        self.ensure_one()
        if self.th_is_enabled:
            self._disable_role()

    def action_view_history(self):
        """Action to view role history"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_role_history').read()[0]
        action['domain'] = [('th_role_line_id', '=', self.id)]
        return action

    # ==================== RECURRENCE METHODS ====================

    def _calculate_next_date(self, base_date):
        """Calculate next date based on recurrence type and interval"""
        self.ensure_one()

        if not base_date:
            return None

        if self.th_recurrence_type == 'daily':
            return base_date + timedelta(days=self.th_recurrence_interval)
        elif self.th_recurrence_type == 'weekly':
            return base_date + timedelta(weeks=self.th_recurrence_interval)
        elif self.th_recurrence_type == 'monthly':
            return base_date + relativedelta(months=self.th_recurrence_interval)

        return None

    def _should_create_recurrence(self):
        """Check if we should create next recurrence"""
        self.ensure_one()

        # Must be recurring and have end date
        if not self.th_is_recurring or not self.th_end_date:
            return False

        # Check if we've reached the count limit
        if self.th_recurrence_count > 0:
            created_count = self.th_recurrence_created_count
            if created_count >= self.th_recurrence_count:
                return False

        # Check if we've reached the end date limit
        if self.th_recurrence_end_date:
            next_date = self._calculate_next_date(self.th_end_date)
            if next_date and next_date > self.th_recurrence_end_date:
                return False

        return True

    def _create_next_recurrence(self):
        """Create the next recurrence in the chain"""
        self.ensure_one()

        if not self._should_create_recurrence():
            return None

        # Calculate next start date (keep same time)
        next_start_date = self._calculate_next_date(self.th_end_date)
        if not next_start_date:
            return None

        # Calculate next end date based on duration
        if self.th_end_date and self.th_start_date:
            # Calculate duration in days
            duration_days = (self.th_end_date - self.th_start_date).days
            next_end_date = next_start_date + timedelta(days=duration_days)
        else:
            # Same day role
            next_end_date = next_start_date

        # Determine parent and sequence
        parent_id = self.th_parent_recurrence_id.id if self.th_parent_recurrence_id else self.id
        sequence = self.th_recurrence_sequence + 1

        # Create new role line using date+time fields
        new_vals = {
            'th_role_id': self.th_role_id.id,
            'th_user_id': self.th_user_id.id,
            'th_start_date': next_start_date,
            'th_start_time': self.th_start_time,
            'th_end_date': next_end_date,
            'th_end_time': self.th_end_time,
            'th_active': self.th_active,
            'th_notes': f"Tự động tạo từ lặp lại #{sequence}",
            'th_parent_recurrence_id': parent_id,
            'th_recurrence_sequence': sequence,
            # Don't copy recurrence settings to child
            'th_is_recurring': False,
        }

        try:
            new_line = self.create(new_vals)
            _logger.info(
                f"Created recurrence #{sequence} for role {self.th_role_id.name} "
                f"for user {self.th_user_id.name}, start date: {next_start_date}, "
                f"time: {self.th_start_time:.2f}-{self.th_end_time:.2f}"
            )
            return new_line
        except Exception as e:
            _logger.error(f"Error creating recurrence: {e}")
            return None

    def _process_recurrence(self):
        """Process recurrence when role ends"""
        self.ensure_one()

        # Only process for original recurring roles (not children)
        if not self.th_is_recurring or self.th_parent_recurrence_id:
            return

        # Check if role has actually ended
        now = fields.Datetime.now()
        if not self.th_date_to or self.th_date_to > now:
            return

        # Create next recurrence
        self._create_next_recurrence()

    @api.model
    def th_process_all_recurrences(self):
        """Cron method to process all pending recurrences"""
        _logger.info("Starting recurrence processing")

        # Find roles that have ended and need recurrence processing
        now = fields.Datetime.now()
        ended_recurring_roles = self.search([
            ('th_is_recurring', '=', True),
            ('th_parent_recurrence_id', '=', False),  # Only original roles
            ('th_date_to', '<=', now),
            ('th_date_to', '!=', False),
        ])

        processed_count = 0
        for role_line in ended_recurring_roles:
            # Check if we already created recurrence for this end time
            existing_recurrence = self.search([
                ('th_parent_recurrence_id', '=', role_line.id),
                ('th_date_from', '>', role_line.th_date_to),
            ], limit=1)

            if not existing_recurrence and role_line._should_create_recurrence():
                new_recurrence = role_line._create_next_recurrence()
                if new_recurrence:
                    processed_count += 1

        _logger.info(f"Recurrence processing completed. {processed_count} new recurrences created.")
        return processed_count

    def action_view_recurrence_chain(self):
        """Action to view all role lines in recurrence chain"""
        self.ensure_one()

        # Get the root recurrence
        root_id = self.th_parent_recurrence_id.id if self.th_parent_recurrence_id else self.id

        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['name'] = 'Chuỗi lặp lại Role'
        action['domain'] = [
            '|',
            ('id', '=', root_id),
            ('th_parent_recurrence_id', '=', root_id)
        ]
        action['context'] = {
            'search_default_group_start_date': 1,
        }
        return action

    def action_create_manual_recurrence(self):
        """Manual action to create next recurrence"""
        self.ensure_one()

        if not self.th_is_recurring:
            raise UserError(_('Role này không được thiết lập để lặp lại'))

        if self.th_parent_recurrence_id:
            raise UserError(_('Chỉ có thể tạo lặp lại từ role gốc'))

        if not self.th_date_to:
            raise UserError(_('Role phải có ngày kết thúc để tạo lặp lại'))

        new_recurrence = self._create_next_recurrence()
        if new_recurrence:
            return {
                'name': _('Role lặp lại đã tạo'),
                'type': 'ir.actions.act_window',
                'res_model': 'th.user.role.line',
                'res_id': new_recurrence.id,
                'view_mode': 'form',
                'target': 'current',
            }
        else:
            raise UserError(_('Không thể tạo lặp lại. Kiểm tra các điều kiện lặp lại.'))

    def action_stop_recurrence(self):
        """Stop recurrence for this role line"""
        self.ensure_one()

        # Get the root role
        root_role = self.th_parent_recurrence_id if self.th_parent_recurrence_id else self

        # Stop recurrence on root
        root_role.write({'th_is_recurring': False})

        # Cancel future recurrences that haven't started yet
        now = fields.Datetime.now()
        future_recurrences = self.search([
            ('th_parent_recurrence_id', '=', root_role.id),
            ('th_date_from', '>', now),
            ('th_is_enabled', '=', False),
        ])

        if future_recurrences:
            future_recurrences.write({'th_active': False})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Đã dừng lặp lại'),
                'message': _('Đã dừng lặp lại và hủy %d role assignments tương lai') % len(future_recurrences),
                'type': 'success',
            }
        }
