<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Extend User Form View -->
        <record id="view_users_form_inherit_th_role" model="ir.ui.view">
            <field name="name">res.users.form.inherit.th.role</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='access_rights']" position="after">
                    <page string="Time-Based Roles" name="th_time_based_roles">
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_role_assignments" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-calendar">
                                <field name="th_role_count" widget="statinfo" string="Total Roles"/>
                            </button>
                            <button name="action_view_role_assignments" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-check">
                                <field name="th_active_role_count" widget="statinfo" string="Active Roles"/>
                            </button>
                            <button name="action_create_role_assignment" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-plus">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Add Role</span>
                                </div>
                            </button>
                            <button name="action_view_role_history" 
                                    type="object" 
                                    class="oe_stat_button" 
                                    icon="fa-history">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">History</span>
                                </div>
                            </button>
                        </div>
                        
                        <group string="Role Summary">
                            <group>
                                <field name="th_role_count" readonly="1"/>
                                <field name="th_active_role_count" readonly="1"/>
                            </group>
                            <group>
                                <field name="th_role_ids" widget="many2many_tags" readonly="1"/>
                                <field name="th_active_role_ids" widget="many2many_tags" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Role Assignments" name="role_assignments">
                                <field name="th_role_line_ids">
                                    <tree decoration-success="th_is_enabled == True" 
                                          decoration-warning="th_status == 'pending'" 
                                          decoration-danger="th_status == 'expired'"
                                          decoration-muted="not th_active">
                                        <field name="th_role_id" required="1"/>
                                        <field name="th_date_from"/>
                                        <field name="th_date_to"/>
                                        <field name="th_status" widget="badge"/>
                                        <field name="th_is_enabled" widget="boolean_toggle"/>
                                        <field name="th_active"/>
                                        <button name="action_enable_role" 
                                                string="Enable" 
                                                type="object" 
                                                icon="fa-play" 
                                                attrs="{'invisible': [('th_is_enabled', '=', True)]}"/>
                                        <button name="action_disable_role" 
                                                string="Disable" 
                                                type="object" 
                                                icon="fa-pause" 
                                                attrs="{'invisible': [('th_is_enabled', '=', False)]}"/>
                                    </tree>
                                    <form>
                                        <header>
                                            <field name="th_status" widget="statusbar"/>
                                        </header>
                                        <group>
                                            <group>
                                                <field name="th_role_id" required="1"/>
                                                <field name="th_start_date"/>
                                                <field name="th_start_time" widget="float_time"/>
                                                <field name="th_active"/>
                                            </group>
                                            <group>
                                                <field name="th_end_date"/>
                                                <field name="th_end_time" widget="float_time"/>
                                                <field name="th_is_enabled" readonly="1"/>
                                            </group>
                                        </group>
                                        <field name="th_notes"/>
                                    </form>
                                </field>
                            </page>
                            
                            <page string="Role History" name="role_history">
                                <field name="th_role_history_ids" readonly="1">
                                    <tree>
                                        <field name="th_date"/>
                                        <field name="th_role_id"/>
                                        <field name="th_action" widget="badge"/>
                                        <field name="th_performed_by"/>
                                        <field name="th_reason"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </page>
                </xpath>
            </field>
        </record>

        <!-- Add Smart Button to User Tree View -->
        <record id="view_users_tree_inherit_th_role" model="ir.ui.view">
            <field name="name">res.users.tree.inherit.th.role</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='login_date']" position="after">
                    <field name="th_active_role_count" string="Active Roles"/>
                </xpath>
            </field>
        </record>

        <!-- Add Filter to User Search View -->
        <!-- <record id="view_users_search_inherit_th_role" model="ir.ui.view">
            <field name="name">res.users.search.inherit.th.role</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='inactive_users']" position="after">
                    <separator/>
                    <filter string="Has Time-Based Roles" name="has_time_roles" domain="[('th_role_count', '>', 0)]"/>
                    <filter string="Has Active Roles" name="has_active_roles" domain="[('th_active_role_count', '>', 0)]"/>
                    <filter string="No Time-Based Roles" name="no_time_roles" domain="[('th_role_count', '=', 0)]"/>
                </xpath>
            </field>
        </record> -->

    </data>
</odoo>
