<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- <PERSON>ron Job for updating role status -->
        <record id="ir_cron_update_role_status" model="ir.cron">
            <field name="name">Update Time-Based Role Status</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="code">model.th_update_role_status()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Daily cleanup cron -->
        <record id="ir_cron_cleanup_role_history" model="ir.cron">
            <field name="name">Cleanup Old Role History</field>
            <field name="model_id" ref="model_th_role_history"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_old_history(days=365)</field>
            <field name="interval_number">7</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Weekly role expiration notification -->
        <!-- <record id="ir_cron_role_expiration_notification" model="ir.cron">
            <field name="name">Role Expiration Notification</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="code"><![CDATA[
# Find roles expiring in the next 7 days
from datetime import datetime, timedelta
now = datetime.now()
expiring_soon = now + timedelta(days=7)

expiring_roles = model.search([
    ('th_date_to', '!=', False),
    ('th_date_to', '>=', now),
    ('th_date_to', '<=', expiring_soon),
    ('th_is_enabled', '=', True)
])

# Send notifications
for role_line in expiring_roles:
    # Notify user
    role_line.message_post(
        subject="Role '{}' expiring soon".format(role_line.th_role_id.name),
        body="Your role '{}' will expire on {}.".format(
            role_line.th_role_id.name, 
            role_line.th_date_to.strftime('%d/%m/%Y %H:%M')
        ),
        partner_ids=[role_line.th_user_id.partner_id.id]
    )
    
    # Notify administrators
    admin_group = env.ref('th_time_based_user_role.group_th_role_manager')
    admin_users = admin_group.users
    if admin_users:
        role_line.message_post(
            subject="Role expiration notification",
            body="Role '{}' for user '{}' will expire on {}.".format(
                role_line.th_role_id.name,
                role_line.th_user_id.name,
                role_line.th_date_to.strftime('%d/%m/%Y %H:%M')
            ),
            partner_ids=admin_users.mapped('partner_id.id')
        )
]]></field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record> -->

    </data>
</odoo>
