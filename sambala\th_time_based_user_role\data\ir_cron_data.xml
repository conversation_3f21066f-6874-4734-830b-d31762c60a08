<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- <PERSON>ron Job for updating role status -->
        <record id="ir_cron_update_role_status" model="ir.cron">
            <field name="name">Update Time-Based Role Status</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="code">model.th_update_role_status()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Daily cleanup cron -->
        <record id="ir_cron_cleanup_role_history" model="ir.cron">
            <field name="name">Cleanup Old Role History</field>
            <field name="model_id" ref="model_th_role_history"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_old_history(days=365)</field>
            <field name="interval_number">7</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>


        <!-- Daily recurrence processing cron -->
        <record id="ir_cron_process_recurrences" model="ir.cron">
            <field name="name">Process Role Recurrences</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="code">model.th_process_all_recurrences()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="doall">True</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="nextcall" eval="(DateTime.now() + timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S')"/>
        </record>


    </data>
</odoo>
