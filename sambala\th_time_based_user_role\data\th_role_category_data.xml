<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default Role Categories -->
        <record id="th_role_category_system" model="th.role.category">
            <field name="name">System Administration</field>
            <field name="description">Roles related to system administration and configuration</field>
        </record>

        <record id="th_role_category_sales" model="th.role.category">
            <field name="name">Sales</field>
            <field name="description">Roles related to sales activities</field>
        </record>

        <record id="th_role_category_hr" model="th.role.category">
            <field name="name">Human Resources</field>
            <field name="description">Roles related to human resources management</field>
        </record>

        <record id="th_role_category_accounting" model="th.role.category">
            <field name="name">Accounting</field>
            <field name="description">Roles related to accounting and finance</field>
        </record>

        <record id="th_role_category_project" model="th.role.category">
            <field name="name">Project Management</field>
            <field name="description">Roles related to project management</field>
        </record>

        <record id="th_role_category_inventory" model="th.role.category">
            <field name="name">Inventory</field>
            <field name="description">Roles related to inventory management</field>
        </record>

        <record id="th_role_category_temporary" model="th.role.category">
            <field name="name">Temporary Access</field>
            <field name="description">Temporary roles for specific periods</field>
        </record>

        <record id="th_role_category_seasonal" model="th.role.category">
            <field name="name">Seasonal Roles</field>
            <field name="description">Roles that are activated during specific seasons</field>
        </record>

        <!-- Sub-categories -->
        <record id="th_role_category_system_backup" model="th.role.category">
            <field name="name">Backup Management</field>
            <field name="description">Roles for backup and recovery operations</field>
            <field name="th_parent_id" ref="th_role_category_system"/>
        </record>

        <record id="th_role_category_system_security" model="th.role.category">
            <field name="name">Security Management</field>
            <field name="description">Roles for security and access control</field>
            <field name="th_parent_id" ref="th_role_category_system"/>
        </record>

        <record id="th_role_category_hr_recruitment" model="th.role.category">
            <field name="name">Recruitment</field>
            <field name="description">Roles for recruitment activities</field>
            <field name="th_parent_id" ref="th_role_category_hr"/>
        </record>

        <record id="th_role_category_hr_payroll" model="th.role.category">
            <field name="name">Payroll</field>
            <field name="description">Roles for payroll management</field>
            <field name="th_parent_id" ref="th_role_category_hr"/>
        </record>

    </data>
</odoo>
