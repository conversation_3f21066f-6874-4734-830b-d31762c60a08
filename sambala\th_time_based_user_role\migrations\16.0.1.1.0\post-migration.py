# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    """
    Migration script to convert existing datetime fields to separate date+time fields
    """
    _logger.info("Starting migration: Converting datetime to date+time fields")
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    try:
        # Check if the new fields exist
        cr.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'th_user_role_line' 
            AND column_name IN ('th_start_date', 'th_start_time', 'th_end_date', 'th_end_time')
        """)
        new_fields = [row[0] for row in cr.fetchall()]
        
        if len(new_fields) < 4:
            _logger.warning("New date+time fields not found. Skipping migration.")
            return
        
        # Get all role lines that need migration
        cr.execute("""
            SELECT id, th_date_from, th_date_to 
            FROM th_user_role_line 
            WHERE th_start_date IS NULL 
            AND th_date_from IS NOT NULL
        """)
        
        role_lines = cr.fetchall()
        _logger.info(f"Found {len(role_lines)} role lines to migrate")
        
        migrated_count = 0
        
        for line_id, date_from, date_to in role_lines:
            try:
                # Convert th_date_from to date + time
                if date_from:
                    start_date = date_from.date()
                    start_time = date_from.hour + date_from.minute / 60.0
                    
                    cr.execute("""
                        UPDATE th_user_role_line 
                        SET th_start_date = %s, th_start_time = %s 
                        WHERE id = %s
                    """, (start_date, start_time, line_id))
                
                # Convert th_date_to to date + time
                if date_to:
                    end_date = date_to.date()
                    end_time = date_to.hour + date_to.minute / 60.0
                    
                    cr.execute("""
                        UPDATE th_user_role_line 
                        SET th_end_date = %s, th_end_time = %s 
                        WHERE id = %s
                    """, (end_date, end_time, line_id))
                
                migrated_count += 1
                
            except Exception as e:
                _logger.error(f"Error migrating role line {line_id}: {e}")
                continue
        
        # Commit the changes
        cr.commit()
        
        _logger.info(f"Migration completed successfully. {migrated_count} role lines migrated.")
        
        # Verify migration
        cr.execute("""
            SELECT COUNT(*) 
            FROM th_user_role_line 
            WHERE th_start_date IS NULL 
            AND th_date_from IS NOT NULL
        """)
        remaining = cr.fetchone()[0]
        
        if remaining > 0:
            _logger.warning(f"{remaining} role lines still need migration")
        else:
            _logger.info("All role lines migrated successfully")
            
    except Exception as e:
        _logger.error(f"Migration failed: {e}")
        cr.rollback()
        raise


def _convert_datetime_to_date_time(datetime_obj):
    """
    Helper function to convert datetime to date and float time
    Returns tuple (date, float_time)
    """
    if not datetime_obj:
        return None, 0.0
    
    date_part = datetime_obj.date()
    time_part = datetime_obj.hour + datetime_obj.minute / 60.0
    
    return date_part, time_part


def _validate_migration(cr):
    """
    Validate that migration was successful
    """
    _logger.info("Validating migration...")
    
    # Check for any inconsistencies
    cr.execute("""
        SELECT id, th_date_from, th_start_date, th_start_time
        FROM th_user_role_line 
        WHERE th_date_from IS NOT NULL 
        AND th_start_date IS NOT NULL
        LIMIT 10
    """)
    
    samples = cr.fetchall()
    
    for line_id, date_from, start_date, start_time in samples:
        expected_date = date_from.date()
        expected_time = date_from.hour + date_from.minute / 60.0
        
        if start_date != expected_date or abs(start_time - expected_time) > 0.01:
            _logger.warning(f"Migration validation failed for line {line_id}")
            return False
    
    _logger.info("Migration validation passed")
    return True
