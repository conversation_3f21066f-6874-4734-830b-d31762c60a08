# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ThRoleCategory(models.Model):
    _name = 'th.role.category'
    _description = '<PERSON><PERSON> mục Role'
    _order = 'name'
    _parent_name = 'th_parent_id'
    _parent_store = True
    _rec_name = 'complete_name'

    name = fields.Char(
        string='Tên danh mục',
        required=True,
        help="Tên của danh mục role"
    )

    description = fields.Text(
        string='Mô tả',
        help="Mô tả của danh mục"
    )

    th_parent_id = fields.Many2one(
        'th.role.category',
        string='Danh mục cha',
        ondelete='cascade',
        help="Danh mục cha"
    )

    th_child_ids = fields.One2many(
        'th.role.category',
        'th_parent_id',
        string='Danh mục con',
        help="Các danh mục con"
    )

    th_role_ids = fields.One2many(
        'th.user.role',
        'th_category_id',
        string='Các Role',
        help="Các role trong danh mục này"
    )

    complete_name = fields.Char(
        string='Tên đầy đủ',
        compute='_compute_complete_name',
        store=True,
        help="Tên đầy đủ với các danh mục cha"
    )

    th_role_count = fields.Integer(
        string='Số lượng Role',
        compute='_compute_th_role_count',
        store=True,
        help="Số lượng role trong danh mục này"
    )
    
    parent_path = fields.Char(index=True)

    @api.depends('name', 'th_parent_id.complete_name')
    def _compute_complete_name(self):
        for category in self:
            if category.th_parent_id:
                category.complete_name = f"{category.th_parent_id.complete_name} / {category.name}"
            else:
                category.complete_name = category.name

    @api.depends('th_role_ids')
    def _compute_th_role_count(self):
        for category in self:
            category.th_role_count = len(category.th_role_ids)

    @api.constrains('th_parent_id')
    def _check_parent_recursion(self):
        if not self._check_recursion():
            raise ValidationError(_('Bạn không thể tạo danh mục đệ quy.'))

    def name_get(self):
        result = []
        for category in self:
            result.append((category.id, category.complete_name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        domain = []
        if name:
            domain = [
                '|', '|',
                ('name', operator, name),
                ('complete_name', operator, name),
                ('description', operator, name)
            ]
        categories = self.search(domain + args, limit=limit)
        return categories.name_get()

    def action_view_roles(self):
        """Hành động xem các role trong danh mục này"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role').read()[0]
        action['domain'] = [('th_category_id', '=', self.id)]
        action['context'] = {
            'default_th_category_id': self.id
        }
        return action
