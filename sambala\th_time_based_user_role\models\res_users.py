# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class ResUsers(models.Model):
    _inherit = 'res.users'

    th_role_line_ids = fields.One2many(
        'th.user.role.line',
        'th_user_id',
        string='Role theo thời gian',
        help="Các role theo thời gian được gán cho user này"
    )

    th_role_ids = fields.Many2many(
        'th.user.role',
        string='Role đã gán',
        compute='_compute_th_role_ids',
        store=True,
        help="Tất cả role được gán cho user này"
    )

    th_active_role_ids = fields.Many2many(
        'th.user.role',
        string='Role đang hoạt động',
        compute='_compute_th_active_role_ids',
        help="Các role hiện đang hoạt động cho user này"
    )

    th_role_count = fields.Integer(
        string='Số lượng Role',
        compute='_compute_th_role_count',
        store=True,
        help="Tổng số role được gán cho user này"
    )

    th_active_role_count = fields.Integer(
        string='Số Role đang hoạt động',
        compute='_compute_th_active_role_count',
        help="Số lượng role hiện đang hoạt động"
    )

    th_role_history_ids = fields.One2many(
        'th.role.history',
        'th_user_id',
        string='Lịch sử Role',
        help="Lịch sử thay đổi role cho user này"
    )

    @api.depends('th_role_line_ids.th_role_id')
    def _compute_th_role_ids(self):
        for user in self:
            user.th_role_ids = user.th_role_line_ids.mapped('th_role_id')

    @api.depends('th_role_line_ids.th_is_enabled', 'th_role_line_ids.th_role_id')
    def _compute_th_active_role_ids(self):
        for user in self:
            active_lines = user.th_role_line_ids.filtered('th_is_enabled')
            user.th_active_role_ids = active_lines.mapped('th_role_id')

    @api.depends('th_role_ids')
    def _compute_th_role_count(self):
        for user in self:
            user.th_role_count = len(user.th_role_ids)

    @api.depends('th_active_role_ids')
    def _compute_th_active_role_count(self):
        for user in self:
            user.th_active_role_count = len(user.th_active_role_ids)

    def action_view_role_assignments(self):
        """Hành động xem phân quyền role cho user này"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['domain'] = [('th_user_id', '=', self.id)]
        action['context'] = {
            'default_th_user_id': self.id
        }
        return action

    def action_view_role_history(self):
        """Hành động xem lịch sử role cho user này"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_role_history').read()[0]
        action['domain'] = [('th_user_id', '=', self.id)]
        return action

    def action_create_role_assignment(self):
        """Hành động tạo phân quyền role mới cho user này"""
        self.ensure_one()
        return {
            'name': _('Tạo phân quyền Role'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role.line',
            'view_mode': 'form',
            'context': {
                'default_th_user_id': self.id
            },
            'target': 'new',
        }

    def th_get_effective_groups(self):
        """Nhận tất cả các nhóm nên được áp dụng cho người dùng này bao gồm các vai trò dựa trên thời gian"""
        self.ensure_one()
        
        # Nhận các nhóm thường xuyên
        regular_groups = self.groups_id
        
        # Get groups từ vai trò đang hoạt động
        role_groups = self.th_active_role_ids.mapped('group_ids')
        
        # Combine và trả lại các nhóm duy nhất
        all_groups = regular_groups | role_groups
        return all_groups

    def th_sync_role_groups(self):
        """Đồng bộ hóa các nhóm người dùng với vai trò dựa trên thời gian hoạt động"""
        self.ensure_one()
        
        # Nhận tất cả các nhóm nên được áp dụng
        target_groups = self.th_get_effective_groups()
        
        # Cập nhật nhóm người dùng
        self.write({'groups_id': [(6, 0, target_groups.ids)]})

    @api.model
    def th_update_all_users_role_status(self):
        """Cập nhật trạng thái vai trò cho tất cả người dùng"""
        users = self.search([])
        for user in users:
            user.th_role_line_ids._check_and_update_role_status()

    def write(self, vals):
        """Override write to handle role-related changes"""
        # Kiểm tra context để tránh bị dệ quy
        if (self.env.context.get('th_disable_role_recursion_guard') or
            self.env.context.get('th_enable_role_recursion_guard')):
            # Bỏ qua ứng dụng lại nhóm vai trò để ngăn chặn đệ quy
            return super().write(vals)

        result = super().write(vals)

        # nếu các nhóm thay đổi hoặc người dùng chọn lại thời gian
        # Lưu lại các nhóm vai trò
        if 'groups_id' in vals and any(user.th_active_role_ids for user in self):
            for user in self:
                if user.th_active_role_ids:
                    # Re-apply các nhóm vai trò có thể đã bị xóa
                    role_groups = user.th_active_role_ids.mapped('group_ids')
                    # Sử dụng bối cảnh để ngăn chặn đệ quy vô hạn
                    user.with_context(th_disable_role_recursion_guard=True).write({
                        'groups_id': [(4, group.id) for group in role_groups]
                    })

        return result
