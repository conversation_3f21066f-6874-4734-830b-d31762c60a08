# -*- coding: utf-8 -*-

from odoo import models, fields, api, _


class ResUsers(models.Model):
    _inherit = 'res.users'

    th_role_line_ids = fields.One2many(
        'th.user.role.line',
        'th_user_id',
        string='Role theo thời gian',
        help="Các role theo thời gian được gán cho user này"
    )

    th_role_ids = fields.Many2many(
        'th.user.role',
        string='Role đã gán',
        compute='_compute_th_role_ids',
        store=True,
        help="Tất cả role được gán cho user này"
    )

    th_active_role_ids = fields.Many2many(
        'th.user.role',
        string='Role đang hoạt động',
        compute='_compute_th_active_role_ids',
        help="Các role hiện đang hoạt động cho user này"
    )

    th_role_count = fields.Integer(
        string='Số lượng Role',
        compute='_compute_th_role_count',
        store=True,
        help="Tổng số role được gán cho user này"
    )

    th_active_role_count = fields.Integer(
        string='Số Role đang hoạt động',
        compute='_compute_th_active_role_count',
        help="Số lượng role hiện đang hoạt động"
    )

    th_role_history_ids = fields.One2many(
        'th.role.history',
        'th_user_id',
        string='Lịch sử Role',
        help="Lịch sử thay đổi role cho user này"
    )

    @api.depends('th_role_line_ids.th_role_id')
    def _compute_th_role_ids(self):
        for user in self:
            user.th_role_ids = user.th_role_line_ids.mapped('th_role_id')

    @api.depends('th_role_line_ids.th_is_enabled', 'th_role_line_ids.th_role_id')
    def _compute_th_active_role_ids(self):
        for user in self:
            active_lines = user.th_role_line_ids.filtered('th_is_enabled')
            user.th_active_role_ids = active_lines.mapped('th_role_id')

    @api.depends('th_role_ids')
    def _compute_th_role_count(self):
        for user in self:
            user.th_role_count = len(user.th_role_ids)

    @api.depends('th_active_role_ids')
    def _compute_th_active_role_count(self):
        for user in self:
            user.th_active_role_count = len(user.th_active_role_ids)

    def action_view_role_assignments(self):
        """Hành động xem phân quyền role cho user này"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['domain'] = [('th_user_id', '=', self.id)]
        action['context'] = {
            'default_th_user_id': self.id
        }
        return action

    def action_view_role_history(self):
        """Hành động xem lịch sử role cho user này"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_role_history').read()[0]
        action['domain'] = [('th_user_id', '=', self.id)]
        return action

    def action_create_role_assignment(self):
        """Hành động tạo phân quyền role mới cho user này"""
        self.ensure_one()
        return {
            'name': _('Tạo phân quyền Role'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role.line',
            'view_mode': 'form',
            'context': {
                'default_th_user_id': self.id
            },
            'target': 'new',
        }

    def th_get_effective_groups(self):
        """Get all groups that should be applied to this user including time-based roles"""
        self.ensure_one()
        
        # Get regular groups
        regular_groups = self.groups_id
        
        # Get groups from active time-based roles
        role_groups = self.th_active_role_ids.mapped('group_ids')
        
        # Combine and return unique groups
        all_groups = regular_groups | role_groups
        return all_groups

    def th_sync_role_groups(self):
        """Synchronize user groups with active time-based roles"""
        self.ensure_one()
        
        # Get all groups that should be applied
        target_groups = self.th_get_effective_groups()
        
        # Update user groups
        self.write({'groups_id': [(6, 0, target_groups.ids)]})

    @api.model
    def th_update_all_users_role_status(self):
        """Update role status for all users"""
        users = self.search([])
        for user in users:
            user.th_role_line_ids._check_and_update_role_status()

    def write(self, vals):
        """Override write to handle role-related changes"""
        result = super().write(vals)
        
        # If groups are being modified and user has time-based roles,
        # we might need to preserve role-based groups
        if 'groups_id' in vals and any(user.th_active_role_ids for user in self):
            for user in self:
                if user.th_active_role_ids:
                    # Re-apply role groups that might have been removed
                    role_groups = user.th_active_role_ids.mapped('group_ids')
                    user.write({'groups_id': [(4, group.id) for group in role_groups]})
        
        return result
