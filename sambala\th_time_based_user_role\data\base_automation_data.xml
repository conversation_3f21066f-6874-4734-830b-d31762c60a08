<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Automation Rule: Activate Role -->
        <record id="base_automation_activate_role" model="base.automation">
            <field name="name">Activate Time-Based Role</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="trigger">on_time</field>
            <field name="trg_date_id" ref="th_time_based_user_role.field_th_user_role_line__th_date_from"/>
            <field name="filter_domain">[('th_active', '=', True), ('th_is_enabled', '=', False)]</field>
            <field name="code">
for record in records:
    record._check_and_update_role_status()
            </field>
            <field name="active">True</field>
        </record>

        <!-- Automation Rule: Deactivate Role -->
        <record id="base_automation_deactivate_role" model="base.automation">
            <field name="name">Deactivate Time-Based Role</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="trigger">on_time</field>
            <field name="trg_date_id" ref="th_time_based_user_role.field_th_user_role_line__th_date_to"/>
            <field name="filter_domain">[('th_active', '=', True), ('th_is_enabled', '=', True)]</field>
            <field name="code">
for record in records:
    record._check_and_update_role_status()
            </field>
            <field name="active">True</field>
        </record>

        <!-- Automation Rule: Log Role Assignment Creation -->
        <record id="base_automation_log_role_creation" model="base.automation">
            <field name="name">Log Role Assignment Creation</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="trigger">on_create</field>
            <field name="code">
for record in records:
    env['th.role.history'].create({
        'th_user_id': record.th_user_id.id,
        'th_role_id': record.th_role_id.id,
        'th_action': 'created',
        'th_role_line_id': record.id,
        'th_reason': 'Role assignment created'
    })
            </field>
            <field name="active">True</field>
        </record>

        <!-- Automation Rule: Log Role Assignment Deletion -->
        <record id="base_automation_log_role_deletion" model="base.automation">
            <field name="name">Log Role Assignment Deletion</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="trigger">on_unlink</field>
            <field name="code">
for record in records:
    env['th.role.history'].create({
        'th_user_id': record.th_user_id.id,
        'th_role_id': record.th_role_id.id,
        'th_action': 'deleted',
        'th_reason': 'Role assignment deleted'
    })
            </field>
            <field name="active">True</field>
        </record>

        <!-- Automation Rule: Send Welcome Email on Role Activation -->
        <record id="base_automation_welcome_email" model="base.automation">
            <field name="name">Send Welcome Email on Role Activation</field>
            <field name="model_id" ref="model_th_user_role_line"/>
            <field name="state">code</field>
            <field name="trigger">on_write</field>
            <field name="filter_domain">[('th_is_enabled', '=', True)]</field>
            <field name="filter_pre_domain">[('th_is_enabled', '=', False)]</field>
            <field name="code">
for record in records:
    if record.th_is_enabled and not record.th_previous_is_enabled:
        # Send notification to user
        record.message_post(
            subject=f"Role '{record.th_role_id.name}' has been activated",
            body=f"""
Hello {record.th_user_id.name},

Your role '{record.th_role_id.name}' has been activated.

Role Details:
- Start Date: {record.th_date_from.strftime('%d/%m/%Y %H:%M') if record.th_date_from else 'N/A'}
- End Date: {record.th_date_to.strftime('%d/%m/%Y %H:%M') if record.th_date_to else 'No End Date'}
- Description: {record.th_role_id.description or 'N/A'}

Best regards,
System Administrator
            """,
            partner_ids=[record.th_user_id.partner_id.id]
        )
            </field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>
