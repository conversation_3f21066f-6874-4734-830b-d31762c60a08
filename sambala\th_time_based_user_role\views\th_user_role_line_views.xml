<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_th_user_role_line_tree" model="ir.ui.view">
            <field name="name">th.user.role.line.tree</field>
            <field name="model">th.user.role.line</field>
            <field name="arch" type="xml">
                <tree string="Role Assignments" 
                      decoration-success="th_is_enabled == True" 
                      decoration-warning="th_status == 'pending'" 
                      decoration-danger="th_status == 'expired'"
                      decoration-muted="not th_active">
                    <field name="th_user_id"/>
                    <field name="th_role_id"/>
                    <field name="th_start_date"/>
                    <field name="th_start_time" widget="float_time"/>
                    <field name="th_end_date"/>
                    <field name="th_end_time" widget="float_time"/>
                    <field name="th_status" widget="badge" 
                           decoration-success="th_status == 'active'"
                           decoration-warning="th_status == 'pending'"
                           decoration-danger="th_status == 'expired'"
                           decoration-muted="th_status == 'disabled'"/>
                    <field name="th_days_until_start" string="Days to Start"/>
                    <field name="th_days_until_end" string="Days to End"/>
                    <field name="th_is_recurring" widget="boolean_toggle"/>
                    <field name="th_recurrence_sequence" string="Seq"/>
                    <field name="th_is_enabled" widget="boolean_toggle"/>
                    <field name="th_active"/>
                    <button name="action_enable_role" 
                            string="Enable" 
                            type="object" 
                            icon="fa-play" 
                            attrs="{'invisible': [('th_is_enabled', '=', True)]}"/>
                    <button name="action_disable_role" 
                            string="Disable" 
                            type="object" 
                            icon="fa-pause" 
                            attrs="{'invisible': [('th_is_enabled', '=', False)]}"/>
                    <button name="action_view_history" 
                            string="History" 
                            type="object" 
                            icon="fa-history"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_th_user_role_line_form" model="ir.ui.view">
            <field name="name">th.user.role.line.form</field>
            <field name="model">th.user.role.line</field>
            <field name="arch" type="xml">
                <form string="Role Assignment">
                    <header>
                        <button name="action_enable_role" 
                                string="Enable Role" 
                                type="object" 
                                class="btn-primary"
                                attrs="{'invisible': [('th_is_enabled', '=', True)]}"/>
                        <button name="action_disable_role" 
                                string="Disable Role" 
                                type="object" 
                                class="btn-secondary"
                                attrs="{'invisible': [('th_is_enabled', '=', False)]}"/>
                        <button name="action_view_history"
                                string="View History"
                                type="object"
                                class="btn-secondary"/>
                        <button name="action_view_recurrence_chain"
                                string="View Recurrence Chain"
                                type="object"
                                class="btn-secondary"
                                attrs="{'invisible': ['&amp;', ('th_is_recurring', '=', False), ('th_parent_recurrence_id', '=', False)]}"/>
                        <button name="action_create_manual_recurrence"
                                string="Create Next Recurrence"
                                type="object"
                                class="btn-secondary"
                                attrs="{'invisible': ['|', ('th_is_recurring', '=', False), ('th_parent_recurrence_id', '!=', False)]}"/>
                        <button name="action_stop_recurrence"
                                string="Stop Recurrence"
                                type="object"
                                class="btn-secondary"
                                attrs="{'invisible': ['&amp;', ('th_is_recurring', '=', False), ('th_parent_recurrence_id', '=', False)]}"/>
                        <field name="th_status" widget="statusbar"
                               statusbar_visible="pending,active,expired"/>
                    </header>
                    <sheet>
                        <widget name="web_ribbon" title="Disabled" bg_color="bg-danger" 
                                attrs="{'invisible': [('th_active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <label for="display_name" class="oe_edit_only"/>
                            <h1>
                                <field name="display_name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="th_user_id" options="{'no_create': True}"/>
                                <field name="th_role_id" options="{'no_create': True}"/>
                                <field name="th_active"/>
                            </group>
                            <group>
                                <field name="th_start_date"/>
                                <field name="th_start_time" widget="float_time"/>
                                <field name="th_end_date"/>
                                <field name="th_end_time" widget="float_time"/>
                                <field name="th_is_enabled" readonly="1"/>
                            </group>
                        </group>
                        
                        <group string="Thông tin trạng thái">
                            <group>
                                <field name="th_days_until_start" readonly="1"/>
                                <field name="th_days_until_end" readonly="1"/>
                            </group>
                            <group>
                                <field name="th_previous_is_enabled" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Groups ứng dụng" name="applied_groups">
                                <field name="th_applied_group_ids" readonly="1">
                                    <tree>
                                        <field name="category_id"/>
                                        <field name="name"/>
                                        <field name="comment"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Lặp lại" name="recurrence"
                                  attrs="{'invisible': [('th_parent_recurrence_id', '!=', False)]}">
                                <group>
                                    <group>
                                        <field name="th_is_recurring"/>
                                        <field name="th_recurrence_type"
                                               attrs="{'invisible': [('th_is_recurring', '=', False)], 'required': [('th_is_recurring', '=', True)]}"/>
                                        <field name="th_recurrence_interval"
                                               attrs="{'invisible': [('th_is_recurring', '=', False)], 'required': [('th_is_recurring', '=', True)]}"/>
                                    </group>
                                    <group>
                                        <field name="th_recurrence_end_date"
                                               attrs="{'invisible': [('th_is_recurring', '=', False)]}"/>
                                        <field name="th_recurrence_count"
                                               attrs="{'invisible': [('th_is_recurring', '=', False)]}"/>
                                        <field name="th_recurrence_created_count" readonly="1"
                                               attrs="{'invisible': [('th_is_recurring', '=', False)]}"/>
                                    </group>
                                </group>

                                <group string="Thông tin chuỗi lặp lại"
                                       attrs="{'invisible': ['&amp;', ('th_parent_recurrence_id', '=', False), ('th_recurrence_created_count', '=', 0)]}">
                                    <group>
                                        <field name="th_parent_recurrence_id" readonly="1"/>
                                        <field name="th_recurrence_sequence" readonly="1"/>
                                    </group>
                                </group>

                                <field name="th_child_recurrence_ids" readonly="1"
                                       attrs="{'invisible': [('th_child_recurrence_ids', '=', [])]}">
                                    <tree>
                                        <field name="th_recurrence_sequence"/>
                                        <field name="th_date_from"/>
                                        <field name="th_date_to"/>
                                        <field name="th_status" widget="badge"/>
                                        <field name="th_is_enabled" widget="boolean_toggle"/>
                                        <field name="th_active"/>
                                    </tree>
                                </field>
                            </page>

                            <page string="Notes" name="notes">
                                <field name="th_notes" placeholder="Additional notes about this role assignment..."/>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Calendar View -->
        <record id="view_th_user_role_line_calendar" model="ir.ui.view">
            <field name="name">th.user.role.line.calendar</field>
            <field name="model">th.user.role.line</field>
            <field name="arch" type="xml">
                <calendar string="Role Assignments Calendar" 
                          date_start="th_date_from" 
                          date_stop="th_date_to" 
                          color="th_user_id"
                          event_open_popup="true"
                          quick_add="false">
                    <field name="display_name"/>
                    <field name="th_user_id"/>
                    <field name="th_role_id"/>
                    <field name="th_status"/>
                </calendar>
            </field>
        </record>


        <!-- Search View -->
        <record id="view_th_user_role_line_search" model="ir.ui.view">
            <field name="name">th.user.role.line.search</field>
            <field name="model">th.user.role.line</field>
            <field name="arch" type="xml">
                <search string="Search Role Assignments">
                    <field name="th_user_id" string="User"/>
                    <field name="th_role_id" string="Role"/>
                    <field name="th_date_from"/>
                    <field name="th_date_to"/>
                    
                    <filter string="Active" name="active" domain="[('th_active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('th_active', '=', False)]"/>
                    
                    <separator/>
                    <filter string="Currently Enabled" name="enabled" domain="[('th_is_enabled', '=', True)]"/>
                    <filter string="Disabled" name="disabled" domain="[('th_is_enabled', '=', False)]"/>
                    
                    <separator/>
                    <filter string="Pending" name="pending" domain="[('th_status', '=', 'pending')]"/>
                    <filter string="Active Status" name="active_status" domain="[('th_status', '=', 'active')]"/>
                    <filter string="Expired" name="expired" domain="[('th_status', '=', 'expired')]"/>
                    
                    <separator/>
                    <filter string="Starting Today" name="starting_today" 
                            domain="[('th_date_from', '&gt;=', (context_today()).strftime('%Y-%m-%d 00:00:00')),
                                     ('th_date_from', '&lt;=', (context_today()).strftime('%Y-%m-%d 23:59:59'))]"/>
                    <filter string="Ending Today" name="ending_today" 
                            domain="[('th_date_to', '&gt;=', (context_today()).strftime('%Y-%m-%d 00:00:00')),
                                     ('th_date_to', '&lt;=', (context_today()).strftime('%Y-%m-%d 23:59:59'))]"/>
                    <filter string="Expiring Soon" name="expiring_soon" 
                            domain="[('th_date_to', '&gt;=', time.strftime('%Y-%m-%d %H:%M:%S')),
                                     ('th_date_to', '&lt;=', (context_today() + relativedelta(days=7)).strftime('%Y-%m-%d 23:59:59'))]"/>
                    
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="User" name="group_user" context="{'group_by': 'th_user_id'}"/>
                        <filter string="Role" name="group_role" context="{'group_by': 'th_role_id'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'th_status'}"/>
                        <filter string="Start Date" name="group_start_date" context="{'group_by': 'th_date_from:month'}"/>
                        <filter string="End Date" name="group_end_date" context="{'group_by': 'th_date_to:month'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action -->
        <record id="action_th_user_role_line" model="ir.actions.act_window">
            <field name="name">Phân quyền Role</field>
            <field name="res_model">th.user.role.line</field>
            <field name="view_mode">tree,form,calendar</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Tạo nhiệm vụ vai trò đầu tiên của bạn!
                </p>
                <p>
                    Role assignments define which users have which roles 
                    and during what time period.
                </p>
            </field>
        </record>

    </data>
</odoo>
