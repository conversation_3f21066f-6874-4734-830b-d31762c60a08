# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class ThUserRole(models.Model):
    _name = 'th.user.role'
    _description = '<PERSON>ai trò người dùng theo thời gian'
    _order = 'th_sequence, name'
    _rec_name = 'name'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(
        string='Tên Role',
        required=True,
        tracking=True,
        help="Tên của role"
    )

    code = fields.Char(
        string='Mã Role',
        required=True,
        tracking=True,
        help="Mã duy nhất cho role"
    )

    description = fields.Text(
        string='Mô tả',
        tracking=True,
        help="Mô tả chi tiết về role"
    )

    active = fields.Boolean(
        string='Kích hoạt',
        default=True,
        tracking=True,
        help="Nếu bỏ chọn, sẽ cho phép ẩn role mà không cần xóa nó."
    )

    group_ids = fields.Many2many(
        'res.groups',
        'th_user_role_group_rel',
        'role_id',
        'group_id',
        string='Nhóm quyền truy cập',
        tracking=True,
        help="Các group sẽ được gán cho user có role này"
    )

    th_role_line_ids = fields.One2many(
        'th.user.role.line',
        'th_role_id',
        string='Phân quyền Role',
        help="Các user được gán role này với ràng buộc thời gian"
    )

    th_user_ids = fields.Many2many(
        'res.users',
        string='Người dùng',
        compute='_compute_th_user_ids',
        store=True,
        help="Các user được gán role này"
    )

    th_category_id = fields.Many2one(
        'th.role.category',
        string='Danh mục',
        tracking=True,
        help="Danh mục của role này"
    )

    th_sequence = fields.Integer(
        string='Thứ tự',
        default=10,
        help="Thứ tự sắp xếp các role"
    )

    th_user_count = fields.Integer(
        string='Số lượng User',
        compute='_compute_th_user_count',
        store=True,
        help="Số lượng user được gán role này"
    )

    th_active_user_count = fields.Integer(
        string='User đang hoạt động',
        compute='_compute_th_active_user_count',
        help="Số lượng user có phân quyền role đang hoạt động"
    )

    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Mã role phải là duy nhất!'),
    ]

    @api.depends('th_role_line_ids.th_user_id')
    def _compute_th_user_ids(self):
        for role in self:
            role.th_user_ids = role.th_role_line_ids.mapped('th_user_id')

    @api.depends('th_user_ids')
    def _compute_th_user_count(self):
        for role in self:
            role.th_user_count = len(role.th_user_ids)

    @api.depends('th_role_line_ids.th_is_enabled')
    def _compute_th_active_user_count(self):
        for role in self:
            role.th_active_user_count = len(
                role.th_role_line_ids.filtered('th_is_enabled')
            )

    @api.constrains('code')
    def _check_code(self):
        for role in self:
            if not role.code:
                raise ValidationError(_('Mã role là bắt buộc'))
            if not role.code.replace('_', '').replace('-', '').isalnum():
                raise ValidationError(
                    _('Mã role chỉ có thể chứa chữ cái, số, dấu gạch dưới và dấu gạch ngang')
                )

    def name_get(self):
        result = []
        for role in self:
            name = f"[{role.code}] {role.name}"
            result.append((role.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        domain = []
        if name:
            domain = [
                '|', '|',
                ('name', operator, name),
                ('code', operator, name),
                ('description', operator, name)
            ]
        roles = self.search(domain + args, limit=limit)
        return roles.name_get()

    def action_view_users(self):
        """Hành động xem các user được gán role này"""
        self.ensure_one()
        action = self.env.ref('base.action_res_users').read()[0]
        action['domain'] = [('id', 'in', self.th_user_ids.ids)]
        action['context'] = {
            'default_th_role_ids': [(6, 0, [self.id])]
        }
        return action

    def action_view_role_lines(self):
        """Hành động xem các phân quyền role"""
        self.ensure_one()
        action = self.env.ref('th_time_based_user_role.action_th_user_role_line').read()[0]
        action['domain'] = [('th_role_id', '=', self.id)]
        action['context'] = {
            'default_th_role_id': self.id
        }
        return action

    def action_create_role_assignment(self):
        """Hành động tạo phân quyền role mới"""
        self.ensure_one()
        return {
            'name': _('Tạo phân quyền Role'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.user.role.line',
            'view_mode': 'form',
            'context': {
                'default_th_role_id': self.id
            },
            'target': 'new',
        }

    def copy(self, default=None):
        """Ghi đè copy để đảm bảo mã duy nhất"""
        default = dict(default or {})
        if 'code' not in default:
            default['code'] = f"{self.code}_copy"
        if 'name' not in default:
            default['name'] = f"{self.name} (Bản sao)"
        return super().copy(default)
